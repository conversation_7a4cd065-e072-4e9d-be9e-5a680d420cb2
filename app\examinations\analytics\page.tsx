"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, Line } from "recharts"
import {
  TrendingUp,
  TrendingDown,
  Users,
  Award,
  Target,
  Clock,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Download,
} from "lucide-react"

// 模拟数据
const performanceMetrics = {
  totalExams: 156,
  totalParticipants: 89,
  averageScore: 82,
  passRate: 78,
  averageTime: 85,
  improvementRate: 12,
}

const difficultyAnalysis = [
  { difficulty: "简单", averageScore: 89, passRate: 95, count: 45 },
  { difficulty: "中等", averageScore: 78, passRate: 82, count: 67 },
  { difficulty: "困难", averageScore: 65, passRate: 58, count: 44 },
]

const timeAnalysis = [
  { timeRange: "0-30分钟", count: 12, averageScore: 65 },
  { timeRange: "30-60分钟", count: 34, averageScore: 78 },
  { timeRange: "60-90分钟", count: 28, averageScore: 85 },
  { timeRange: "90-120分钟", count: 15, averageScore: 88 },
]

const questionTypeAnalysis = [
  { type: "单选题", totalQuestions: 450, correctRate: 82, averageTime: 1.2 },
  { type: "多选题", totalQuestions: 280, correctRate: 68, averageTime: 2.1 },
  { type: "判断题", totalQuestions: 320, correctRate: 89, averageTime: 0.8 },
  { type: "填空题", totalQuestions: 150, correctRate: 72, averageTime: 1.8 },
]

const weaknessAnalysis = [
  { topic: "消防设备操作", errorRate: 35, frequency: 89, difficulty: "高" },
  { topic: "应急程序", errorRate: 28, frequency: 76, difficulty: "中" },
  { topic: "安全规程", errorRate: 22, frequency: 92, difficulty: "中" },
  { topic: "法规条文", errorRate: 31, frequency: 65, difficulty: "高" },
  { topic: "求生技能", errorRate: 18, frequency: 54, difficulty: "低" },
]

const learningPathRecommendations = [
  {
    student: "张三",
    weakAreas: ["消防设备操作", "应急程序"],
    recommendedCourses: ["消防安全进阶", "应急处理实训"],
    priority: "高",
    estimatedTime: "2周",
  },
  {
    student: "李四",
    weakAreas: ["法规条文", "安全规程"],
    recommendedCourses: ["海事法规详解", "安全操作规范"],
    priority: "中",
    estimatedTime: "3周",
  },
  {
    student: "王五",
    weakAreas: ["求生技能"],
    recommendedCourses: ["海上求生训练"],
    priority: "低",
    estimatedTime: "1周",
  },
]

export default function ExamAnalyticsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("本月")
  const [selectedCategory, setSelectedCategory] = useState("全部分类")

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">考试分析</h2>
          <p className="text-muted-foreground">深度分析考试数据，提供智能化学习建议</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="本周">本周</SelectItem>
              <SelectItem value="本月">本月</SelectItem>
              <SelectItem value="本季度">本季度</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出分析
          </Button>
        </div>
      </div>

      {/* 关键指标 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">考试总数</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceMetrics.totalExams}</div>
            <p className="text-xs text-muted-foreground">+8% 较上月</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">参考人数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceMetrics.totalParticipants}</div>
            <p className="text-xs text-muted-foreground">活跃学员</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均分数</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceMetrics.averageScore}</div>
            <div className="flex items-center text-xs text-green-600">
              <TrendingUp className="h-3 w-3 mr-1" />
              +5% 较上月
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">通过率</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceMetrics.passRate}%</div>
            <div className="flex items-center text-xs text-green-600">
              <TrendingUp className="h-3 w-3 mr-1" />
              +3% 较上月
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均用时</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceMetrics.averageTime}分</div>
            <div className="flex items-center text-xs text-red-600">
              <TrendingDown className="h-3 w-3 mr-1" />
              -2分钟
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">提升率</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{performanceMetrics.improvementRate}%</div>
            <p className="text-xs text-muted-foreground">重考提升</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="difficulty" className="space-y-4">
        <TabsList>
          <TabsTrigger value="difficulty">难度分析</TabsTrigger>
          <TabsTrigger value="time">时间分析</TabsTrigger>
          <TabsTrigger value="questions">题型分析</TabsTrigger>
          <TabsTrigger value="weakness">薄弱环节</TabsTrigger>
          <TabsTrigger value="recommendations">学习建议</TabsTrigger>
        </TabsList>

        <TabsContent value="difficulty" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>难度分布分析</CardTitle>
                <CardDescription>不同难度级别的考试表现对比</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    averageScore: {
                      label: "平均分",
                      color: "hsl(var(--chart-1))",
                    },
                    passRate: {
                      label: "通过率",
                      color: "hsl(var(--chart-2))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={difficultyAnalysis}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="difficulty" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="averageScore" fill="var(--color-averageScore)" />
                      <Bar dataKey="passRate" fill="var(--color-passRate)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>难度详细统计</CardTitle>
                <CardDescription>各难度级别的具体数据</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {difficultyAnalysis.map((item) => (
                    <div key={item.difficulty} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium">{item.difficulty}</h4>
                        <Badge variant="outline">{item.count} 次考试</Badge>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">平均分</span>
                            <span className="text-sm font-medium">{item.averageScore}</span>
                          </div>
                          <Progress value={item.averageScore} className="h-2" />
                        </div>
                        <div>
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm">通过率</span>
                            <span className="text-sm font-medium">{item.passRate}%</span>
                          </div>
                          <Progress value={item.passRate} className="h-2" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="time" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>答题时间分析</CardTitle>
              <CardDescription>不同答题时间段的成绩表现</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                config={{
                  count: {
                    label: "人数",
                    color: "hsl(var(--chart-1))",
                  },
                  averageScore: {
                    label: "平均分",
                    color: "hsl(var(--chart-2))",
                  },
                }}
                className="h-[400px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={timeAnalysis}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="timeRange" />
                    <YAxis yAxisId="left" />
                    <YAxis yAxisId="right" orientation="right" />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar yAxisId="left" dataKey="count" fill="var(--color-count)" />
                    <Line yAxisId="right" dataKey="averageScore" stroke="var(--color-averageScore)" />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>题型表现分析</CardTitle>
              <CardDescription>不同题型的正确率和答题时间统计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {questionTypeAnalysis.map((type) => (
                  <div key={type.type} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">{type.type}</h4>
                      <Badge variant="outline">{type.totalQuestions} 题</Badge>
                    </div>

                    <div className="grid grid-cols-3 gap-4">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm">正确率</span>
                          <span className="text-sm font-medium">{type.correctRate}%</span>
                        </div>
                        <Progress value={type.correctRate} className="h-2" />
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm">平均用时</span>
                          <span className="text-sm font-medium">{type.averageTime}分钟</span>
                        </div>
                        <Progress value={(type.averageTime / 3) * 100} className="h-2" />
                      </div>
                      <div className="flex items-center justify-center">
                        <Badge
                          className={
                            type.correctRate >= 80
                              ? "bg-green-100 text-green-800"
                              : type.correctRate >= 60
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-red-100 text-red-800"
                          }
                        >
                          {type.correctRate >= 80 ? "优秀" : type.correctRate >= 60 ? "良好" : "需改进"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="weakness" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>薄弱知识点分析</CardTitle>
              <CardDescription>识别学员普遍存在的薄弱环节</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {weaknessAnalysis.map((weakness, index) => (
                  <div key={weakness.topic} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center space-x-2">
                        <span
                          className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold text-white ${
                            index < 2 ? "bg-red-500" : index < 4 ? "bg-yellow-500" : "bg-green-500"
                          }`}
                        >
                          {index + 1}
                        </span>
                        <h4 className="font-medium">{weakness.topic}</h4>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge
                          className={
                            weakness.difficulty === "高"
                              ? "bg-red-100 text-red-800"
                              : weakness.difficulty === "中"
                                ? "bg-yellow-100 text-yellow-800"
                                : "bg-green-100 text-green-800"
                          }
                        >
                          {weakness.difficulty}难度
                        </Badge>
                        <AlertTriangle
                          className={`h-4 w-4 ${
                            weakness.errorRate > 30
                              ? "text-red-500"
                              : weakness.errorRate > 20
                                ? "text-yellow-500"
                                : "text-green-500"
                          }`}
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm">错误率</span>
                          <span className="text-sm font-medium text-red-600">{weakness.errorRate}%</span>
                        </div>
                        <Progress value={weakness.errorRate} className="h-2" />
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm">出现频次</span>
                          <span className="text-sm font-medium">{weakness.frequency} 次</span>
                        </div>
                        <Progress value={(weakness.frequency / 100) * 100} className="h-2" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="recommendations" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>个性化学习建议</CardTitle>
              <CardDescription>基于考试表现的智能学习路径推荐</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {learningPathRecommendations.map((rec) => (
                  <div key={rec.student} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">{rec.student}</h4>
                      <Badge
                        className={
                          rec.priority === "高"
                            ? "bg-red-100 text-red-800"
                            : rec.priority === "中"
                              ? "bg-yellow-100 text-yellow-800"
                              : "bg-green-100 text-green-800"
                        }
                      >
                        {rec.priority}优先级
                      </Badge>
                    </div>

                    <div className="space-y-3">
                      <div>
                        <h5 className="text-sm font-medium mb-1">薄弱领域:</h5>
                        <div className="flex flex-wrap gap-1">
                          {rec.weakAreas.map((area) => (
                            <Badge key={area} variant="outline" className="text-xs">
                              {area}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div>
                        <h5 className="text-sm font-medium mb-1">推荐课程:</h5>
                        <div className="flex flex-wrap gap-1">
                          {rec.recommendedCourses.map((course) => (
                            <Badge key={course} className="text-xs bg-blue-100 text-blue-800">
                              {course}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>预计学习时间: {rec.estimatedTime}</span>
                        <Button size="sm" variant="outline">
                          生成学习计划
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
