"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Anchor, Building, User } from "lucide-react"
import Link from "next/link"

export default function RegisterPage() {
  const [step, setStep] = useState(1)

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-cyan-50 p-4">
      <div className="w-full max-w-2xl">
        <div className="flex flex-col items-center mb-8">
          <Link href="/" className="flex items-center justify-center mb-4">
            <Anchor className="h-10 w-10 text-blue-600" />
            <span className="ml-2 text-2xl font-bold text-gray-900">MaritimePro</span>
          </Link>
          <p className="text-gray-600 text-center">创建您的专业培训账户</p>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>企业注册</CardTitle>
            <CardDescription>步骤 {step} / 3 - 请填写您的企业和个人信息</CardDescription>
          </CardHeader>
          <CardContent>
            {step === 1 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center">
                  <Building className="h-5 w-5 mr-2" />
                  企业信息
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companyName">公司名称 *</Label>
                    <Input id="companyName" placeholder="中远海运集团有限公司" required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="companyType">企业类型</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择企业类型" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="shipping">航运公司</SelectItem>
                        <SelectItem value="port">港口企业</SelectItem>
                        <SelectItem value="logistics">物流公司</SelectItem>
                        <SelectItem value="training">培训机构</SelectItem>
                        <SelectItem value="other">其他</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="industry">主营业务</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择主营业务" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="hazmat">危化品运输</SelectItem>
                        <SelectItem value="oil">油品运输</SelectItem>
                        <SelectItem value="container">集装箱运输</SelectItem>
                        <SelectItem value="bulk">散货运输</SelectItem>
                        <SelectItem value="mixed">综合运输</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="fleetSize">船队规模</Label>
                    <Select>
                      <SelectTrigger>
                        <SelectValue placeholder="选择船队规模" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="small">1-10艘</SelectItem>
                        <SelectItem value="medium">11-50艘</SelectItem>
                        <SelectItem value="large">51-100艘</SelectItem>
                        <SelectItem value="xlarge">100艘以上</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="address">公司地址</Label>
                  <Input id="address" placeholder="上海市浦东新区世纪大道1500号" />
                </div>
                <Button className="w-full" onClick={() => setStep(2)}>
                  下一步
                </Button>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  管理员信息
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">姓 *</Label>
                    <Input id="firstName" placeholder="张" required />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">名 *</Label>
                    <Input id="lastName" placeholder="三" required />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="position">职位</Label>
                    <Input id="position" placeholder="培训经理" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="department">部门</Label>
                    <Input id="department" placeholder="人力资源部" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">邮箱地址 *</Label>
                  <Input id="email" type="email" placeholder="<EMAIL>" required />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="phone">联系电话</Label>
                    <Input id="phone" placeholder="+86 138 0000 0000" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="workPhone">办公电话</Label>
                    <Input id="workPhone" placeholder="021-12345678" />
                  </div>
                </div>
                <div className="flex gap-4">
                  <Button variant="outline" onClick={() => setStep(1)}>
                    上一步
                  </Button>
                  <Button className="flex-1" onClick={() => setStep(3)}>
                    下一步
                  </Button>
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">培训需求</h3>
                <div className="space-y-3">
                  <Label>您的企业需要哪些培训课程？（可多选）</Label>
                  <div className="grid grid-cols-2 gap-3">
                    {[
                      { id: "hazmat-basic", label: "危化品基础培训" },
                      { id: "hazmat-advanced", label: "危化品高级培训" },
                      { id: "oil-operations", label: "油品操作培训" },
                      { id: "stcw-basic", label: "STCW基本安全培训" },
                      { id: "pollution-prevention", label: "污染防治培训" },
                      { id: "emergency-response", label: "应急响应培训" },
                    ].map((course) => (
                      <div key={course.id} className="flex items-center space-x-2">
                        <Checkbox id={course.id} />
                        <Label htmlFor={course.id} className="text-sm">
                          {course.label}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="trainees">预计培训人数</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择培训人数范围" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">1-50人</SelectItem>
                      <SelectItem value="medium">51-200人</SelectItem>
                      <SelectItem value="large">201-500人</SelectItem>
                      <SelectItem value="xlarge">500人以上</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="timeline">期望开始时间</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="选择开始时间" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="immediate">立即开始</SelectItem>
                      <SelectItem value="month">1个月内</SelectItem>
                      <SelectItem value="quarter">3个月内</SelectItem>
                      <SelectItem value="later">稍后确定</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="password">设置密码 *</Label>
                  <Input id="password" type="password" placeholder="请设置一个强密码" required />
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox id="terms" />
                  <Label htmlFor="terms" className="text-sm">
                    我已阅读并同意{" "}
                    <Link href="/terms" className="text-blue-600 hover:underline">
                      服务条款
                    </Link>{" "}
                    和{" "}
                    <Link href="/privacy" className="text-blue-600 hover:underline">
                      隐私政策
                    </Link>
                  </Label>
                </div>
                <div className="flex gap-4">
                  <Button variant="outline" onClick={() => setStep(2)}>
                    上一步
                  </Button>
                  <Button className="flex-1 bg-blue-600 hover:bg-blue-700" asChild>
                    <Link href="/dashboard">完成注册</Link>
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        <div className="text-center mt-6 text-sm text-gray-500">
          已有账户？{" "}
          <Link href="/login" className="text-blue-600 hover:underline">
            立即登录
          </Link>
        </div>
      </div>
    </div>
  )
}
