"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, ResponsiveContainer, LineChart, Line } from "recharts"
import { Search, Download, Users, Award, Target, CheckCircle, XCircle } from "lucide-react"

// 模拟数据
const examResults = [
  {
    id: 1,
    examTitle: "船舶安全操作基础考试",
    category: "安全培训",
    totalParticipants: 45,
    passedCount: 37,
    failedCount: 8,
    averageScore: 78,
    highestScore: 96,
    lowestScore: 42,
    passRate: 82,
    averageTime: 75,
    date: "2024-01-15",
  },
  {
    id: 2,
    examTitle: "海上求生技能测试",
    category: "求生技能",
    totalParticipants: 32,
    passedCount: 28,
    failedCount: 4,
    averageScore: 85,
    highestScore: 98,
    lowestScore: 58,
    passRate: 88,
    averageTime: 68,
    date: "2024-01-14",
  },
  {
    id: 3,
    examTitle: "消防安全综合评估",
    category: "消防安全",
    totalParticipants: 28,
    passedCount: 25,
    failedCount: 3,
    averageScore: 89,
    highestScore: 100,
    lowestScore: 65,
    passRate: 89,
    averageTime: 95,
    date: "2024-01-13",
  },
]

const scoreDistribution = [
  { range: "0-59", count: 8, color: "#ef4444" },
  { range: "60-69", count: 12, color: "#f97316" },
  { range: "70-79", count: 25, color: "#eab308" },
  { range: "80-89", count: 32, color: "#22c55e" },
  { range: "90-100", count: 18, color: "#3b82f6" },
]

const monthlyTrend = [
  { month: "10月", passRate: 75, averageScore: 72 },
  { month: "11月", passRate: 78, averageScore: 75 },
  { month: "12月", passRate: 82, averageScore: 78 },
  { month: "1月", passRate: 86, averageScore: 84 },
]

const categoryPerformance = [
  { category: "安全培训", passRate: 82, averageScore: 78, participants: 45 },
  { category: "求生技能", passRate: 88, averageScore: 85, participants: 32 },
  { category: "消防安全", passRate: 89, averageScore: 89, participants: 28 },
  { category: "法规培训", passRate: 76, averageScore: 72, participants: 38 },
]

export default function ExamResultsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("全部分类")
  const [selectedPeriod, setSelectedPeriod] = useState("本月")

  const categories = ["全部分类", "安全培训", "求生技能", "消防安全", "法规培训"]

  const filteredResults = examResults.filter((result) => {
    const matchesSearch = result.examTitle.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "全部分类" || result.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  // 统计数据
  const totalParticipants = examResults.reduce((sum, r) => sum + r.totalParticipants, 0)
  const totalPassed = examResults.reduce((sum, r) => sum + r.passedCount, 0)
  const totalFailed = examResults.reduce((sum, r) => sum + r.failedCount, 0)
  const overallPassRate = Math.round((totalPassed / totalParticipants) * 100)
  const overallAverageScore = Math.round(examResults.reduce((sum, r) => sum + r.averageScore, 0) / examResults.length)

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">考试结果</h2>
          <p className="text-muted-foreground">考试成绩统计和分析报告</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="本周">本周</SelectItem>
              <SelectItem value="本月">本月</SelectItem>
              <SelectItem value="本季度">本季度</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索考试..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择分类" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总参考人数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalParticipants}</div>
            <p className="text-xs text-muted-foreground">+12% 较上月</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">通过人数</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalPassed}</div>
            <p className="text-xs text-muted-foreground">通过率 {overallPassRate}%</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">未通过人数</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalFailed}</div>
            <p className="text-xs text-muted-foreground">需要重考</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均分数</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallAverageScore}</div>
            <p className="text-xs text-muted-foreground">+3 较上月</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">通过率</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{overallPassRate}%</div>
            <p className="text-xs text-muted-foreground">+4% 较上月</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="distribution">分数分布</TabsTrigger>
          <TabsTrigger value="trend">趋势分析</TabsTrigger>
          <TabsTrigger value="category">分类分析</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* 考试结果列表 */}
          <Card>
            <CardHeader>
              <CardTitle>考试结果详情</CardTitle>
              <CardDescription>显示各项考试的详细统计信息</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredResults.map((result) => (
                  <div key={result.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-medium">{result.examTitle}</h4>
                        <p className="text-sm text-muted-foreground">
                          {result.category} • {result.date}
                        </p>
                      </div>
                      <Badge
                        className={
                          result.passRate >= 80 ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800"
                        }
                      >
                        通过率 {result.passRate}%
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                      <div className="text-center">
                        <p className="text-2xl font-bold text-blue-600">{result.totalParticipants}</p>
                        <p className="text-xs text-muted-foreground">参考人数</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-green-600">{result.passedCount}</p>
                        <p className="text-xs text-muted-foreground">通过</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold text-red-600">{result.failedCount}</p>
                        <p className="text-xs text-muted-foreground">未通过</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold">{result.averageScore}</p>
                        <p className="text-xs text-muted-foreground">平均分</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold">{result.highestScore}</p>
                        <p className="text-xs text-muted-foreground">最高分</p>
                      </div>
                      <div className="text-center">
                        <p className="text-2xl font-bold">{result.averageTime}分钟</p>
                        <p className="text-xs text-muted-foreground">平均用时</p>
                      </div>
                    </div>

                    <div className="mt-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm">通过率</span>
                        <span className="text-sm font-medium">{result.passRate}%</span>
                      </div>
                      <Progress value={result.passRate} className="h-2" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>分数分布</CardTitle>
              <CardDescription>显示考试分数的分布情况</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                config={{
                  count: {
                    label: "人数",
                    color: "hsl(var(--chart-1))",
                  },
                }}
                className="h-[400px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={scoreDistribution}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="range" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Bar dataKey="count" fill="var(--color-count)" />
                  </BarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trend" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>成绩趋势</CardTitle>
              <CardDescription>显示通过率和平均分数的月度趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                config={{
                  passRate: {
                    label: "通过率",
                    color: "hsl(var(--chart-1))",
                  },
                  averageScore: {
                    label: "平均分",
                    color: "hsl(var(--chart-2))",
                  },
                }}
                className="h-[400px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={monthlyTrend}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Line
                      type="monotone"
                      dataKey="passRate"
                      stroke="var(--color-passRate)"
                      strokeWidth={2}
                      name="通过率(%)"
                    />
                    <Line
                      type="monotone"
                      dataKey="averageScore"
                      stroke="var(--color-averageScore)"
                      strokeWidth={2}
                      name="平均分"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="category" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>分类表现</CardTitle>
              <CardDescription>不同培训分类的考试表现对比</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categoryPerformance.map((category) => (
                  <div key={category.category} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium">{category.category}</h4>
                      <Badge variant="outline">{category.participants} 人参考</Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-4 mb-3">
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm">通过率</span>
                          <span className="text-sm font-medium">{category.passRate}%</span>
                        </div>
                        <Progress value={category.passRate} className="h-2" />
                      </div>
                      <div>
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm">平均分</span>
                          <span className="text-sm font-medium">{category.averageScore}</span>
                        </div>
                        <Progress value={category.averageScore} className="h-2" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
