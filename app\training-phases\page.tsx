"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import {
  Search,
  Plus,
  Calendar,
  Users,
  Clock,
  Target,
  TrendingUp,
  CheckCircle,
  Play,
  Pause,
  MoreHorizontal,
  BookOpen,
  MapPin,
  Filter,
  Eye,
  Edit,
  Trash2,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from "recharts"

const trainingPhases = [
  {
    id: 1,
    name: "化学品船基础安全培训",
    description: "针对新入职船员的化学品船基础安全知识培训",
    category: "化学品船安全",
    status: "进行中",
    startDate: "2024-01-15",
    endDate: "2024-03-15",
    duration: "8周",
    totalParticipants: 45,
    completedParticipants: 28,
    progress: 62,
    instructor: "张教官",
    location: "上海培训中心",
    modules: [
      { name: "化学品分类", status: "已完成", participants: 45 },
      { name: "安全操作程序", status: "进行中", participants: 28 },
      { name: "应急处理", status: "计划中", participants: 0 },
      { name: "实操演练", status: "计划中", participants: 0 },
    ],
    budget: 150000,
    spent: 93000,
    nextMilestone: "模块2考核",
    milestoneDate: "2024-02-10",
  },
  {
    id: 2,
    name: "油轮高级操作认证",
    description: "面向有经验船员的油轮高级操作技能认证培训",
    category: "油轮操作",
    status: "计划中",
    startDate: "2024-02-20",
    endDate: "2024-04-20",
    duration: "9周",
    totalParticipants: 32,
    completedParticipants: 0,
    progress: 0,
    instructor: "李专家",
    location: "青岛培训基地",
    modules: [
      { name: "高级装卸技术", status: "计划中", participants: 0 },
      { name: "污染防控", status: "计划中", participants: 0 },
      { name: "设备维护", status: "计划中", participants: 0 },
      { name: "认证考试", status: "计划中", participants: 0 },
    ],
    budget: 200000,
    spent: 0,
    nextMilestone: "培训启动",
    milestoneDate: "2024-02-20",
  },
  {
    id: 3,
    name: "STCW基本安全更新培训",
    description: "STCW公约要求的基本安全培训更新课程",
    category: "STCW基本安全",
    status: "已完成",
    startDate: "2023-11-01",
    endDate: "2024-01-01",
    duration: "9周",
    totalParticipants: 78,
    completedParticipants: 76,
    progress: 97,
    instructor: "王船长",
    location: "大连海事学院",
    modules: [
      { name: "个人求生技术", status: "已完成", participants: 78 },
      { name: "防火与灭火", status: "已完成", participants: 76 },
      { name: "基本急救", status: "已完成", participants: 76 },
      { name: "个人安全与社会责任", status: "已完成", participants: 76 },
    ],
    budget: 120000,
    spent: 118500,
    nextMilestone: "证书颁发",
    milestoneDate: "2024-01-05",
  },
]

const progressData = [
  { week: "第1周", planned: 100, actual: 95 },
  { week: "第2周", planned: 100, actual: 98 },
  { week: "第3周", planned: 100, actual: 92 },
  { week: "第4周", planned: 100, actual: 88 },
  { week: "第5周", planned: 100, actual: 85 },
  { week: "第6周", planned: 100, actual: 82 },
  { week: "第7周", planned: 100, actual: 78 },
  { week: "第8周", planned: 100, actual: 75 },
]

const categoryData = [
  { name: "化学品船", value: 35, color: "#ef4444" },
  { name: "油轮操作", value: 28, color: "#3b82f6" },
  { name: "STCW基本", value: 42, color: "#10b981" },
  { name: "应急响应", value: 18, color: "#f59e0b" },
]

export default function TrainingPhasesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterStatus, setFilterStatus] = useState("all")
  const [filterCategory, setFilterCategory] = useState("all")

  const filteredPhases = trainingPhases.filter((phase) => {
    const matchesSearch =
      phase.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      phase.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      phase.instructor.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = filterStatus === "all" || phase.status === filterStatus
    const matchesCategory = filterCategory === "all" || phase.category === filterCategory
    return matchesSearch && matchesStatus && matchesCategory
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case "进行中":
        return "bg-blue-100 text-blue-700"
      case "已完成":
        return "bg-green-100 text-green-700"
      case "计划中":
        return "bg-orange-100 text-orange-700"
      case "已暂停":
        return "bg-gray-100 text-gray-700"
      case "已取消":
        return "bg-red-100 text-red-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }

  const getModuleStatusIcon = (status: string) => {
    switch (status) {
      case "已完成":
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case "进行中":
        return <Play className="h-4 w-4 text-blue-500" />
      case "计划中":
        return <Clock className="h-4 w-4 text-gray-400" />
      default:
        return <Clock className="h-4 w-4 text-gray-400" />
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">培训阶段管理</h1>
            <p className="text-gray-600">管理和跟踪所有培训阶段的进度</p>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="outline">
              <Calendar className="w-4 h-4 mr-2" />
              培训日历
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  创建培训阶段
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>创建新培训阶段</DialogTitle>
                  <DialogDescription>设置新的培训阶段计划</DialogDescription>
                </DialogHeader>
                <div className="grid gap-6 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phase-name">阶段名称</Label>
                      <Input id="phase-name" placeholder="化学品船基础安全培训" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="category">培训类别</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择类别" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="chemical">化学品船安全</SelectItem>
                          <SelectItem value="oil">油轮操作</SelectItem>
                          <SelectItem value="stcw">STCW基本安全</SelectItem>
                          <SelectItem value="emergency">应急响应</SelectItem>
                          <SelectItem value="environmental">环境保护</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="description">阶段描述</Label>
                    <Textarea id="description" placeholder="详细描述培训阶段的目标和内容..." className="min-h-[80px]" />
                  </div>
                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="start-date">开始日期</Label>
                      <Input id="start-date" type="date" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="end-date">结束日期</Label>
                      <Input id="end-date" type="date" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="participants">参与人数</Label>
                      <Input id="participants" type="number" placeholder="45" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="instructor">培训教官</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择教官" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="zhang">张教官</SelectItem>
                          <SelectItem value="li">李专家</SelectItem>
                          <SelectItem value="wang">王船长</SelectItem>
                          <SelectItem value="zhao">赵主任</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="location">培训地点</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择地点" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="shanghai">上海培训中心</SelectItem>
                          <SelectItem value="qingdao">青岛培训基地</SelectItem>
                          <SelectItem value="dalian">大连海事学院</SelectItem>
                          <SelectItem value="guangzhou">广州培训中心</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="budget">预算金额（元）</Label>
                    <Input id="budget" type="number" placeholder="150000" />
                  </div>
                  <div className="space-y-4">
                    <Label>培训模块</Label>
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Input placeholder="模块名称" className="flex-1" />
                        <Input placeholder="预计时长（小时）" type="number" className="w-32" />
                        <Button size="sm" variant="outline">
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                      <div className="flex items-center gap-2">
                        <Input placeholder="模块名称" className="flex-1" />
                        <Input placeholder="预计时长（小时）" type="number" className="w-32" />
                        <Button size="sm" variant="outline">
                          <Plus className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                    <Button size="sm" variant="ghost">
                      <Plus className="w-4 h-4 mr-2" />
                      添加更多模块
                    </Button>
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline">取消</Button>
                  <Button>创建阶段</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </header>

      <div className="flex-1 p-6 space-y-6">
        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">活跃阶段</CardTitle>
              <Calendar className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">8</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+2</span> 本月新增
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">参与学员</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">234</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-blue-600">67%</span> 完成率
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均进度</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">73%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+5%</span> 较上周
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">预算执行</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">85%</div>
              <p className="text-xs text-muted-foreground">预算使用率</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="phases" className="space-y-6">
          <TabsList>
            <TabsTrigger value="phases">培训阶段</TabsTrigger>
            <TabsTrigger value="analytics">数据分析</TabsTrigger>
            <TabsTrigger value="schedule">时间安排</TabsTrigger>
            <TabsTrigger value="resources">资源管理</TabsTrigger>
          </TabsList>

          <TabsContent value="phases" className="space-y-6">
            {/* Search and Filter */}
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="按名称、描述或教官搜索..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                  <Select value={filterStatus} onValueChange={setFilterStatus}>
                    <SelectTrigger className="w-40">
                      <Filter className="w-4 h-4 mr-2" />
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="进行中">进行中</SelectItem>
                      <SelectItem value="计划中">计划中</SelectItem>
                      <SelectItem value="已完成">已完成</SelectItem>
                      <SelectItem value="已暂停">已暂停</SelectItem>
                    </SelectContent>
                  </Select>
                  <Select value={filterCategory} onValueChange={setFilterCategory}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="全部类别" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部类别</SelectItem>
                      <SelectItem value="化学品船安全">化学品船安全</SelectItem>
                      <SelectItem value="油轮操作">油轮操作</SelectItem>
                      <SelectItem value="STCW基本安全">STCW基本安全</SelectItem>
                      <SelectItem value="应急响应">应急响应</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Training Phases List */}
            <div className="grid gap-6">
              {filteredPhases.map((phase) => (
                <Card key={phase.id} className="hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <CardTitle className="text-lg">{phase.name}</CardTitle>
                          <Badge className={getStatusColor(phase.status)}>{phase.status}</Badge>
                          <Badge variant="outline">{phase.category}</Badge>
                        </div>
                        <CardDescription className="text-sm">{phase.description}</CardDescription>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            <Eye className="w-4 h-4 mr-2" />
                            查看详情
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Edit className="w-4 h-4 mr-2" />
                            编辑阶段
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            <Users className="w-4 h-4 mr-2" />
                            管理学员
                          </DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">
                            <Trash2 className="w-4 h-4 mr-2" />
                            删除阶段
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid gap-6 md:grid-cols-2">
                      {/* Phase Info */}
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4 text-gray-400" />
                            <span>
                              {phase.startDate} - {phase.endDate}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span>{phase.duration}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4 text-gray-400" />
                            <span>
                              {phase.completedParticipants}/{phase.totalParticipants} 学员
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4 text-gray-400" />
                            <span>{phase.location}</span>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>整体进度</span>
                            <span className="font-medium">{phase.progress}%</span>
                          </div>
                          <Progress value={phase.progress} className="h-2" />
                        </div>

                        <div className="space-y-2">
                          <div className="flex justify-between text-sm">
                            <span>预算执行</span>
                            <span className="font-medium">
                              ¥{phase.spent.toLocaleString()} / ¥{phase.budget.toLocaleString()}
                            </span>
                          </div>
                          <Progress value={(phase.spent / phase.budget) * 100} className="h-2" />
                        </div>

                        <div className="p-3 bg-blue-50 rounded-lg">
                          <div className="flex items-center gap-2 text-sm">
                            <Target className="h-4 w-4 text-blue-500" />
                            <span className="font-medium text-blue-900">下一里程碑:</span>
                            <span className="text-blue-700">{phase.nextMilestone}</span>
                          </div>
                          <p className="text-xs text-blue-600 mt-1">预计时间: {phase.milestoneDate}</p>
                        </div>
                      </div>

                      {/* Modules */}
                      <div className="space-y-3">
                        <h4 className="font-medium flex items-center gap-2">
                          <BookOpen className="h-4 w-4" />
                          培训模块
                        </h4>
                        <div className="space-y-2">
                          {phase.modules.map((module, index) => (
                            <div key={index} className="flex items-center justify-between p-2 border rounded">
                              <div className="flex items-center gap-2">
                                {getModuleStatusIcon(module.status)}
                                <span className="text-sm font-medium">{module.name}</span>
                              </div>
                              <div className="flex items-center gap-2">
                                <span className="text-xs text-gray-500">{module.participants}人</span>
                                <Badge
                                  variant="outline"
                                  className={
                                    module.status === "已完成"
                                      ? "border-green-200 text-green-700"
                                      : module.status === "进行中"
                                        ? "border-blue-200 text-blue-700"
                                        : "border-gray-200 text-gray-700"
                                  }
                                >
                                  {module.status}
                                </Badge>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-6 pt-4 border-t">
                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <span>教官: {phase.instructor}</span>
                        <span>•</span>
                        <span>创建时间: {phase.startDate}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        {phase.status === "进行中" && (
                          <Button size="sm" variant="outline">
                            <Pause className="w-4 h-4 mr-2" />
                            暂停
                          </Button>
                        )}
                        {phase.status === "计划中" && (
                          <Button size="sm">
                            <Play className="w-4 h-4 mr-2" />
                            开始
                          </Button>
                        )}
                        <Button size="sm" variant="outline">
                          查看详情
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Progress Trend */}
              <Card>
                <CardHeader>
                  <CardTitle>进度趋势分析</CardTitle>
                  <CardDescription>计划vs实际完成情况</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <LineChart data={progressData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="week" />
                      <YAxis />
                      <Tooltip />
                      <Line
                        type="monotone"
                        dataKey="planned"
                        stroke="#94a3b8"
                        strokeWidth={2}
                        name="计划进度"
                        strokeDasharray="5 5"
                      />
                      <Line type="monotone" dataKey="actual" stroke="#3b82f6" strokeWidth={2} name="实际进度" />
                    </LineChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Category Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>培训类别分布</CardTitle>
                  <CardDescription>按类别的培训阶段数量</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={categoryData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}`}
                      >
                        {categoryData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Performance Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>绩效指标</CardTitle>
                <CardDescription>关键培训绩效指标</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-green-600">92%</div>
                    <p className="text-sm text-gray-500">按时完成率</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">87%</div>
                    <p className="text-sm text-gray-500">学员满意度</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-orange-600">¥2.3M</div>
                    <p className="text-sm text-gray-500">总预算</p>
                  </div>
                  <div className="text-center p-4 border rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">15天</div>
                    <p className="text-sm text-gray-500">平均周期</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="schedule" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>培训时间表</CardTitle>
                <CardDescription>所有培训阶段的时间安排</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {trainingPhases.map((phase) => (
                    <div key={phase.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-4 h-4 rounded-full bg-blue-500"></div>
                        <div>
                          <p className="font-medium">{phase.name}</p>
                          <p className="text-sm text-gray-500">
                            {phase.startDate} - {phase.endDate} • {phase.instructor}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <Badge className={getStatusColor(phase.status)}>{phase.status}</Badge>
                        <span className="text-sm text-gray-500">{phase.totalParticipants}人</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="resources" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Instructor Allocation */}
              <Card>
                <CardHeader>
                  <CardTitle>教官分配</CardTitle>
                  <CardDescription>教官工作负荷分布</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { name: "张教官", phases: 3, students: 125, utilization: 85 },
                      { name: "李专家", phases: 2, students: 89, utilization: 70 },
                      { name: "王船长", phases: 2, students: 156, utilization: 95 },
                      { name: "赵主任", phases: 1, students: 45, utilization: 45 },
                    ].map((instructor, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{instructor.name}</span>
                          <span className="text-sm text-gray-500">
                            {instructor.phases}个阶段 • {instructor.students}名学员
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Progress value={instructor.utilization} className="flex-1" />
                          <span className="text-sm font-medium">{instructor.utilization}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Facility Usage */}
              <Card>
                <CardHeader>
                  <CardTitle>设施使用情况</CardTitle>
                  <CardDescription>培训场地使用率</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {[
                      { name: "上海培训中心", usage: 78, capacity: "200人" },
                      { name: "青岛培训基地", usage: 65, capacity: "150人" },
                      { name: "大连海事学院", usage: 92, capacity: "300人" },
                      { name: "广州培训中心", usage: 45, capacity: "180人" },
                    ].map((facility, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{facility.name}</span>
                          <span className="text-sm text-gray-500">容量: {facility.capacity}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Progress value={facility.usage} className="flex-1" />
                          <span className="text-sm font-medium">{facility.usage}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
