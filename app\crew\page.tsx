"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Search,
  Filter,
  Plus,
  MoreHorizontal,
  User,
  Ship,
  MapPin,
  FileText,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

const crewMembers = [
  {
    id: 1,
    name: "张三",
    rank: "轮机长",
    vessel: "化学品船-海洋号",
    email: "<EMAIL>",
    phone: "+86-138-0000-0001",
    nationality: "中国",
    joinDate: "2022-03-15",
    certifications: [
      { name: "化学品船操作", status: "有效", expires: "2025-03-15" },
      { name: "STCW基本安全", status: "有效", expires: "2024-12-20" },
      { name: "高级消防", status: "已过期", expires: "2023-11-10" },
    ],
    trainingProgress: 85,
    lastTraining: "2024-01-15",
    performance: "优秀",
    status: "在职",
  },
  {
    id: 2,
    name: "李四",
    rank: "二副",
    vessel: "油轮-探索号",
    email: "<EMAIL>",
    phone: "+86-138-0000-0002",
    nationality: "中国",
    joinDate: "2021-08-22",
    certifications: [
      { name: "油轮安全", status: "即将到期", expires: "2024-02-20" },
      { name: "STCW基本安全", status: "有效", expires: "2025-08-22" },
      { name: "雷达导航", status: "有效", expires: "2024-11-15" },
    ],
    trainingProgress: 92,
    lastTraining: "2024-01-20",
    performance: "优秀",
    status: "在职",
  },
  {
    id: 3,
    name: "王五",
    rank: "一水",
    vessel: "化学品船-先锋号",
    email: "<EMAIL>",
    phone: "+86-138-0000-0003",
    nationality: "中国",
    joinDate: "2023-01-10",
    certifications: [
      { name: "STCW基本安全", status: "有效", expires: "2026-01-10" },
      { name: "化学品处理", status: "培训中", expires: "2024-06-15" },
    ],
    trainingProgress: 67,
    lastTraining: "2024-01-18",
    performance: "良好",
    status: "在职",
  },
  {
    id: 4,
    name: "赵六",
    rank: "三管轮",
    vessel: "油轮-航行者号",
    email: "<EMAIL>",
    phone: "+86-138-0000-0004",
    nationality: "中国",
    joinDate: "2022-11-05",
    certifications: [
      { name: "机舱安全", status: "有效", expires: "2025-11-05" },
      { name: "化学品船操作", status: "有效", expires: "2024-09-12" },
      { name: "高级消防", status: "有效", expires: "2025-02-28" },
    ],
    trainingProgress: 78,
    lastTraining: "2024-01-12",
    performance: "良好",
    status: "休假",
  },
]

export default function CrewManagementPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCrew, setSelectedCrew] = useState(null)
  const [filterStatus, setFilterStatus] = useState("all")

  const filteredCrew = crewMembers.filter((member) => {
    const matchesSearch =
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.rank.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.vessel.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesFilter = filterStatus === "all" || member.status.toLowerCase() === filterStatus
    return matchesSearch && matchesFilter
  })

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "有效":
        return "bg-green-100 text-green-700"
      case "已过期":
        return "bg-red-100 text-red-700"
      case "即将到期":
        return "bg-orange-100 text-orange-700"
      case "培训中":
        return "bg-blue-100 text-blue-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }

  const getPerformanceColor = (performance: string) => {
    switch (performance.toLowerCase()) {
      case "优秀":
        return "text-green-600"
      case "良好":
        return "text-blue-600"
      case "一般":
        return "text-orange-600"
      case "较差":
        return "text-red-600"
      default:
        return "text-gray-600"
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">船员管理</h1>
            <p className="text-gray-600">管理船员档案、证书和培训记录</p>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="outline">
              <FileText className="w-4 h-4 mr-2" />
              导出数据
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  添加船员
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>添加新船员</DialogTitle>
                  <DialogDescription>输入新船员的详细信息</DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">姓名</Label>
                      <Input id="name" placeholder="张三" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="rank">职务</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择职务" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="captain">船长</SelectItem>
                          <SelectItem value="chief-engineer">轮机长</SelectItem>
                          <SelectItem value="first-officer">大副</SelectItem>
                          <SelectItem value="second-officer">二副</SelectItem>
                          <SelectItem value="third-engineer">三管轮</SelectItem>
                          <SelectItem value="able-seaman">一水</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="vessel">分配船舶</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择船舶" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="mt-horizon">化学品船-海洋号</SelectItem>
                          <SelectItem value="mt-explorer">油轮-探索号</SelectItem>
                          <SelectItem value="mt-pioneer">化学品船-先锋号</SelectItem>
                          <SelectItem value="mt-voyager">油轮-航行者号</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="nationality">国籍</Label>
                      <Input id="nationality" placeholder="中国" />
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="email">邮箱</Label>
                      <Input id="email" type="email" placeholder="<EMAIL>" />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="phone">电话</Label>
                      <Input id="phone" placeholder="+86-138-0000-0001" />
                    </div>
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline">取消</Button>
                  <Button>添加船员</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </header>

      <div className="flex-1 p-6 space-y-6">
        {/* Search and Filter */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="按姓名、职务或船舶搜索..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-48">
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="在职">在职</SelectItem>
                  <SelectItem value="休假">休假</SelectItem>
                  <SelectItem value="离职">离职</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Crew Statistics */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">船员总数</CardTitle>
              <User className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{crewMembers.length}</div>
              <p className="text-xs text-muted-foreground">在职人员</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">证书到期</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-600">12</div>
              <p className="text-xs text-muted-foreground">30天内</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">培训进度</CardTitle>
              <TrendingUp className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">78%</div>
              <p className="text-xs text-muted-foreground">平均完成度</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">绩效表现</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">94%</div>
              <p className="text-xs text-muted-foreground">优秀/良好评级</p>
            </CardContent>
          </Card>
        </div>

        {/* Crew List */}
        <Card>
          <CardHeader>
            <CardTitle>船员列表</CardTitle>
            <CardDescription>所有船员的完整列表和详细信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredCrew.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">{member.name.charAt(0)}</span>
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <p className="font-medium">{member.name}</p>
                        <Badge variant={member.status === "在职" ? "default" : "secondary"}>{member.status}</Badge>
                      </div>
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {member.rank}
                        </span>
                        <span className="flex items-center gap-1">
                          <Ship className="h-3 w-3" />
                          {member.vessel}
                        </span>
                        <span className="flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          {member.nationality}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-6">
                    <div className="text-center">
                      <p className="text-sm font-medium">{member.trainingProgress}%</p>
                      <p className="text-xs text-gray-500">培训</p>
                    </div>
                    <div className="text-center">
                      <p className={`text-sm font-medium ${getPerformanceColor(member.performance)}`}>
                        {member.performance}
                      </p>
                      <p className="text-xs text-gray-500">绩效</p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm font-medium">
                        {member.certifications.filter((c) => c.status === "有效").length}
                      </p>
                      <p className="text-xs text-gray-500">有效证书</p>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>查看档案</DropdownMenuItem>
                        <DropdownMenuItem>编辑信息</DropdownMenuItem>
                        <DropdownMenuItem>培训历史</DropdownMenuItem>
                        <DropdownMenuItem>发送消息</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
