"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Progress } from "@/components/ui/progress"
import {
  Brain,
  Upload,
  FileText,
  Wand2,
  CheckCircle,
  Clock,
  AlertCircle,
  Download,
  RefreshCw,
  Sparkles,
  Target,
  BookOpen,
  Zap,
} from "lucide-react"

const generationHistory = [
  {
    id: 1,
    title: "化学品船安全 - 模块3",
    category: "化学品船安全",
    questionsGenerated: 25,
    difficulty: "中等",
    status: "已完成",
    timestamp: "2024-01-20 14:30",
    successRate: 87,
    sourceDocument: "IMDG规则_2022.pdf",
  },
  {
    id: 2,
    title: "应急响应程序",
    category: "应急响应",
    questionsGenerated: 18,
    difficulty: "困难",
    status: "进行中",
    timestamp: "2024-01-20 15:45",
    successRate: null,
    sourceDocument: "应急手册_v3.pdf",
  },
  {
    id: 3,
    title: "油轮操作 - 基础",
    category: "油轮操作",
    questionsGenerated: 32,
    difficulty: "简单",
    status: "已完成",
    timestamp: "2024-01-19 09:15",
    successRate: 92,
    sourceDocument: "油轮操作指南.pdf",
  },
]

export default function AIQuestionGenerationPage() {
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [isGenerating, setIsGenerating] = useState(false)

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setSelectedFile(file)
    }
  }

  const startGeneration = () => {
    setIsGenerating(true)
    setGenerationProgress(0)

    // 模拟AI生成进度
    const interval = setInterval(() => {
      setGenerationProgress((prev) => {
        if (prev >= 100) {
          clearInterval(interval)
          setIsGenerating(false)
          return 100
        }
        return prev + 10
      })
    }, 500)
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "已完成":
        return "bg-green-100 text-green-700"
      case "进行中":
        return "bg-blue-100 text-blue-700"
      case "失败":
        return "bg-red-100 text-red-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Brain className="h-6 w-6 text-purple-600" />
              AI题目生成
            </h1>
            <p className="text-gray-600">使用人工智能生成考试题目</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge className="bg-purple-100 text-purple-700">
              <Sparkles className="w-3 h-3 mr-1" />
              AI驱动
            </Badge>
          </div>
        </div>
      </header>

      <div className="flex-1 p-6 space-y-6">
        {/* AI Statistics */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">生成题目数</CardTitle>
              <Wand2 className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">1,247</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+156</span> 本月
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均质量评分</CardTitle>
              <Target className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">89.5%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+3.2%</span> 提升
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">处理时间</CardTitle>
              <Zap className="h-4 w-4 text-yellow-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">2.3秒</div>
              <p className="text-xs text-muted-foreground">每题平均</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">成功率</CardTitle>
              <CheckCircle className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">94.2%</div>
              <p className="text-xs text-muted-foreground">题目通过率</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="generate" className="space-y-6">
          <TabsList>
            <TabsTrigger value="generate">生成题目</TabsTrigger>
            <TabsTrigger value="history">生成历史</TabsTrigger>
            <TabsTrigger value="settings">AI设置</TabsTrigger>
          </TabsList>

          <TabsContent value="generate" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Question Generation Form */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Wand2 className="h-5 w-5 text-purple-500" />
                    生成新题目
                  </CardTitle>
                  <CardDescription>上传学习资料并配置AI生成参数</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="file-upload">上传学习资料</Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                      <input
                        id="file-upload"
                        type="file"
                        accept=".pdf,.doc,.docx,.txt"
                        onChange={handleFileUpload}
                        className="hidden"
                      />
                      <label htmlFor="file-upload" className="cursor-pointer">
                        <Upload className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                        <p className="text-sm text-gray-600">
                          {selectedFile ? selectedFile.name : "点击上传或拖拽文件"}
                        </p>
                        <p className="text-xs text-gray-400">支持PDF、DOC、DOCX、TXT，最大10MB</p>
                      </label>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="category">类别</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择类别" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="chemical">化学品船安全</SelectItem>
                          <SelectItem value="oil">油轮操作</SelectItem>
                          <SelectItem value="stcw">STCW基本安全</SelectItem>
                          <SelectItem value="emergency">应急响应</SelectItem>
                          <SelectItem value="environmental">环境保护</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="difficulty">难度等级</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择难度" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="easy">简单</SelectItem>
                          <SelectItem value="medium">中等</SelectItem>
                          <SelectItem value="hard">困难</SelectItem>
                          <SelectItem value="expert">专家</SelectItem>
                          <SelectItem value="mixed">混合难度</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="question-count">题目数量</Label>
                    <Input id="question-count" type="number" placeholder="25" min="1" max="100" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="question-types">题目类型</Label>
                    <div className="grid grid-cols-2 gap-2">
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" defaultChecked />
                        <span className="text-sm">单选题</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" defaultChecked />
                        <span className="text-sm">判断题</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" />
                        <span className="text-sm">填空题</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input type="checkbox" />
                        <span className="text-sm">问答题</span>
                      </label>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="focus-areas">重点领域（可选）</Label>
                    <Textarea id="focus-areas" placeholder="指定特定主题或重点关注的领域..." className="min-h-[80px]" />
                  </div>

                  <Button className="w-full" onClick={startGeneration} disabled={!selectedFile || isGenerating}>
                    {isGenerating ? (
                      <>
                        <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                        生成中...
                      </>
                    ) : (
                      <>
                        <Wand2 className="w-4 h-4 mr-2" />
                        生成题目
                      </>
                    )}
                  </Button>

                  {isGenerating && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>生成进度</span>
                        <span>{generationProgress}%</span>
                      </div>
                      <Progress value={generationProgress} className="w-full" />
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* AI Insights */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-blue-500" />
                    AI生成洞察
                  </CardTitle>
                  <CardDescription>实时分析和建议</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900 flex items-center gap-2">
                      <BookOpen className="h-4 w-4" />
                      内容分析
                    </h4>
                    <p className="text-sm text-blue-700 mt-1">
                      上传的资料包含化学品船安全程序的全面覆盖。AI建议生成20-30道题目以获得最佳覆盖率。
                    </p>
                  </div>

                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900 flex items-center gap-2">
                      <Target className="h-4 w-4" />
                      质量预测
                    </h4>
                    <p className="text-sm text-green-700 mt-1">
                      基于类似资料，生成的题目预计有92%的通过率和85%的学员平均正确率。
                    </p>
                  </div>

                  <div className="p-4 bg-orange-50 rounded-lg">
                    <h4 className="font-medium text-orange-900 flex items-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      建议
                    </h4>
                    <ul className="text-sm text-orange-700 mt-1 space-y-1">
                      <li>• 包含更多实际场景题目</li>
                      <li>• 重点关注应急响应程序</li>
                      <li>• 添加视觉图表题目</li>
                    </ul>
                  </div>

                  <div className="p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-medium text-purple-900 flex items-center gap-2">
                      <Sparkles className="h-4 w-4" />
                      AI增强
                    </h4>
                    <p className="text-sm text-purple-700 mt-1">题目将自动增强详细解释、难度校准和学习目标对齐。</p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>生成历史</CardTitle>
                <CardDescription>跟踪所有AI题目生成会话</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {generationHistory.map((session) => (
                    <div key={session.id} className="border rounded-lg p-4 hover:bg-gray-50">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-medium">{session.title}</h3>
                            <Badge className={getStatusColor(session.status)}>
                              {session.status === "进行中" && <RefreshCw className="w-3 h-3 mr-1 animate-spin" />}
                              {session.status === "已完成" && <CheckCircle className="w-3 h-3 mr-1" />}
                              {session.status}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            <span className="flex items-center gap-1">
                              <FileText className="h-3 w-3" />
                              {session.questionsGenerated} 道题目
                            </span>
                            <span className="flex items-center gap-1">
                              <Target className="h-3 w-3" />
                              {session.difficulty} 难度
                            </span>
                            <span className="flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {session.timestamp}
                            </span>
                            {session.successRate && (
                              <span className="flex items-center gap-1">
                                <CheckCircle className="h-3 w-3" />
                                {session.successRate}% 正确率
                              </span>
                            )}
                          </div>
                          <p className="text-xs text-gray-500 mt-1">来源: {session.sourceDocument}</p>
                        </div>
                        <div className="flex items-center gap-2">
                          {session.status === "已完成" && (
                            <Button size="sm" variant="outline">
                              <Download className="w-4 h-4 mr-2" />
                              导出
                            </Button>
                          )}
                          <Button size="sm" variant="outline">
                            查看详情
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>AI模型配置</CardTitle>
                  <CardDescription>配置AI生成参数</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="model">AI模型</Label>
                    <Select defaultValue="gpt-4">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="gpt-4">GPT-4 (推荐)</SelectItem>
                        <SelectItem value="gpt-3.5">GPT-3.5 Turbo</SelectItem>
                        <SelectItem value="claude">Claude-3</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="creativity">创造性水平</Label>
                    <Select defaultValue="balanced">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="conservative">保守</SelectItem>
                        <SelectItem value="balanced">平衡</SelectItem>
                        <SelectItem value="creative">创新</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="quality-threshold">质量阈值</Label>
                    <Select defaultValue="high">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="standard">标准 (80%)</SelectItem>
                        <SelectItem value="high">高 (90%)</SelectItem>
                        <SelectItem value="premium">优质 (95%)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input type="checkbox" id="auto-review" defaultChecked />
                    <Label htmlFor="auto-review">启用自动质量审查</Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input type="checkbox" id="explanations" defaultChecked />
                    <Label htmlFor="explanations">生成详细解释</Label>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>生成偏好</CardTitle>
                  <CardDescription>自定义题目生成行为</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="default-count">默认题目数量</Label>
                    <Input id="default-count" type="number" defaultValue="25" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="language">主要语言</Label>
                    <Select defaultValue="chinese">
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="chinese">中文</SelectItem>
                        <SelectItem value="english">英语</SelectItem>
                        <SelectItem value="spanish">西班牙语</SelectItem>
                        <SelectItem value="french">法语</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>首选题目分布</Label>
                    <div className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm">单选题</span>
                        <span className="text-sm">60%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">判断题</span>
                        <span className="text-sm">25%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">填空题</span>
                        <span className="text-sm">10%</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm">问答题</span>
                        <span className="text-sm">5%</span>
                      </div>
                    </div>
                  </div>

                  <Button className="w-full">保存设置</Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
