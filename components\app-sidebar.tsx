"use client"
import {
  Bar<PERSON>hart3,
  Users,
  FileQuestion,
  BookOpen,
  Calendar,
  Settings,
  Ship,
  Brain,
  Award,
  ClipboardList,
  TrendingUp,
  UserCheck,
  FileText,
  GraduationCap,
  ChevronDown,
} from "lucide-react"
import type React from "react"

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import Link from "next/link"
import { usePathname } from "next/navigation"

const menuItems = [
  {
    title: "数据看板",
    url: "/",
    icon: BarChart3,
  },
  {
    title: "船员管理",
    url: "/crew",
    icon: Users,
  },
  {
    title: "考试管理",
    icon: FileQuestion,
    items: [
      {
        title: "题库管理",
        url: "/examinations/questions",
        icon: FileQuestion,
      },
      {
        title: "试卷管理",
        url: "/examinations/papers",
        icon: ClipboardList,
      },
      {
        title: "已提交试卷",
        url: "/examinations/submissions",
        icon: FileText,
      },
      {
        title: "考试结果",
        url: "/examinations/results",
        icon: Award,
      },
      {
        title: "考试分析",
        url: "/examinations/analytics",
        icon: TrendingUp,
      },
      {
        title: "未考试人员",
        url: "/examinations/untested",
        icon: UserCheck,
      },
    ],
  },
  {
    title: "学习管理",
    icon: BookOpen,
    items: [
      {
        title: "学习资料",
        url: "/learning/materials",
        icon: BookOpen,
      },
      {
        title: "学习记录",
        url: "/learning/records",
        icon: GraduationCap,
      },
      {
        title: "学习总结",
        url: "/learning/summary",
        icon: TrendingUp,
      },
    ],
  },
  {
    title: "培训阶段",
    url: "/training-phases",
    icon: Calendar,
  },
  {
    title: "AI功能",
    icon: Brain,
    items: [
      {
        title: "题目生成",
        url: "/ai/question-generation",
        icon: Brain,
      },
      {
        title: "学习路径",
        url: "/ai/learning-paths",
        icon: TrendingUp,
      },
      {
        title: "性能分析",
        url: "/ai/analysis",
        icon: BarChart3,
      },
    ],
  },
  {
    title: "证书管理",
    url: "/certifications",
    icon: Award,
  },
  {
    title: "系统设置",
    url: "/settings",
    icon: Settings,
  },
]

export function AppSidebar() {
  const pathname = usePathname()

  const isActive = (url: string) => {
    if (url === "/") {
      return pathname === "/"
    }
    return pathname.startsWith(url)
  }

  const hasActiveChild = (items: any[]) => {
    return items?.some((item) => isActive(item.url))
  }

  return (
    <Sidebar
      collapsible="icon"
      className="bg-slate-900 border-slate-800"
      style={
        {
          "--sidebar-background": "15 23 42",
          "--sidebar-foreground": "226 232 240",
          "--sidebar-primary": "59 130 246",
          "--sidebar-primary-foreground": "248 250 252",
          "--sidebar-accent": "30 41 59",
          "--sidebar-accent-foreground": "226 232 240",
          "--sidebar-border": "30 41 59",
          "--sidebar-ring": "59 130 246",
        } as React.CSSProperties
      }
    >
      <SidebarHeader className="bg-slate-900 border-b border-slate-800">
        <div className="flex items-center gap-3 px-4 py-3">
          <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg">
            <Ship className="h-5 w-5 text-white" />
          </div>
          <div className="group-data-[collapsible=icon]:hidden">
            <h1 className="text-lg font-bold text-white">船员培训系统</h1>
            <p className="text-xs text-slate-400">Maritime Crew Training</p>
          </div>
        </div>
      </SidebarHeader>

      <SidebarContent className="bg-slate-900">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  {item.items ? (
                    <Collapsible className="group/collapsible" defaultOpen={hasActiveChild(item.items)}>
                      <CollapsibleTrigger asChild>
                        <SidebarMenuButton
                          tooltip={item.title}
                          className={`
                            text-slate-300 hover:text-white hover:bg-slate-800
                            ${hasActiveChild(item.items) ? "bg-slate-800 text-white" : ""}
                          `}
                        >
                          <item.icon className="h-4 w-4" />
                          <span>{item.title}</span>
                          <ChevronDown className="ml-auto h-4 w-4 transition-transform group-data-[state=open]/collapsible:rotate-180" />
                        </SidebarMenuButton>
                      </CollapsibleTrigger>
                      <CollapsibleContent>
                        <SidebarMenuSub>
                          {item.items.map((subItem) => (
                            <SidebarMenuSubItem key={subItem.title}>
                              <SidebarMenuSubButton
                                asChild
                                tooltip={subItem.title}
                                className={`
                                  text-slate-400 hover:text-white hover:bg-slate-800
                                  ${isActive(subItem.url) ? "bg-blue-600 text-white" : ""}
                                `}
                              >
                                <Link href={subItem.url}>
                                  <subItem.icon className="h-4 w-4" />
                                  <span>{subItem.title}</span>
                                </Link>
                              </SidebarMenuSubButton>
                            </SidebarMenuSubItem>
                          ))}
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    </Collapsible>
                  ) : (
                    <SidebarMenuButton
                      asChild
                      tooltip={item.title}
                      className={`
                        text-slate-300 hover:text-white hover:bg-slate-800
                        ${isActive(item.url) ? "bg-blue-600 text-white" : ""}
                      `}
                    >
                      <Link href={item.url}>
                        <item.icon className="h-4 w-4" />
                        <span>{item.title}</span>
                      </Link>
                    </SidebarMenuButton>
                  )}
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>

      <SidebarFooter className="bg-slate-900 border-t border-slate-800">
        <div className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-sm font-medium text-white">管</span>
            </div>
            <div className="group-data-[collapsible=icon]:hidden">
              <p className="text-sm font-medium text-white">管理员</p>
              <p className="text-xs text-slate-400">系统管理员</p>
            </div>
          </div>
        </div>
      </SidebarFooter>
    </Sidebar>
  )
}
