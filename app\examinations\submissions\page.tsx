"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Search, Eye, Download, FileText, CheckCircle, XCircle, AlertTriangle, Award } from "lucide-react"

// 模拟数据
const submissions = [
  {
    id: 1,
    studentName: "张三",
    studentId: "ST001",
    examTitle: "船舶安全操作基础考试",
    category: "安全培训",
    submissionTime: "2024-01-15 10:30:00",
    duration: 85,
    totalQuestions: 50,
    answeredQuestions: 50,
    correctAnswers: 42,
    score: 84,
    totalScore: 100,
    passScore: 60,
    status: "passed",
    attempt: 1,
    timeSpent: 85,
  },
  {
    id: 2,
    studentName: "李四",
    studentId: "ST002",
    examTitle: "海上求生技能测试",
    category: "求生技能",
    submissionTime: "2024-01-14 15:45:00",
    duration: 75,
    totalQuestions: 40,
    answeredQuestions: 38,
    correctAnswers: 25,
    score: 62,
    totalScore: 100,
    passScore: 70,
    status: "failed",
    attempt: 2,
    timeSpent: 73,
  },
  {
    id: 3,
    studentName: "王五",
    studentId: "ST003",
    examTitle: "消防安全综合评估",
    category: "消防安全",
    submissionTime: "2024-01-13 16:45:00",
    duration: 120,
    totalQuestions: 60,
    answeredQuestions: 60,
    correctAnswers: 55,
    score: 92,
    totalScore: 100,
    passScore: 75,
    status: "passed",
    attempt: 1,
    timeSpent: 118,
  },
  {
    id: 4,
    studentName: "赵六",
    studentId: "ST004",
    examTitle: "国际海事法规考核",
    category: "法规培训",
    submissionTime: "2024-01-12 11:20:00",
    duration: 100,
    totalQuestions: 45,
    answeredQuestions: 43,
    correctAnswers: 32,
    score: 71,
    totalScore: 100,
    passScore: 65,
    status: "passed",
    attempt: 3,
    timeSpent: 95,
  },
  {
    id: 5,
    studentName: "钱七",
    studentId: "ST005",
    examTitle: "船舶安全操作基础考试",
    category: "安全培训",
    submissionTime: "2024-01-11 09:15:00",
    duration: 90,
    totalQuestions: 50,
    answeredQuestions: 45,
    correctAnswers: 28,
    score: 56,
    totalScore: 100,
    passScore: 60,
    status: "failed",
    attempt: 1,
    timeSpent: 88,
  },
]

const students = ["全部学员", "张三", "李四", "王五", "赵六", "钱七"]
const categories = ["全部分类", "安全培训", "求生技能", "消防安全", "法规培训"]
const statuses = ["全部状态", "通过", "未通过"]

export default function ExamSubmissionsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStudent, setSelectedStudent] = useState("全部学员")
  const [selectedCategory, setSelectedCategory] = useState("全部分类")
  const [selectedStatus, setSelectedStatus] = useState("全部状态")
  const [selectedSubmission, setSelectedSubmission] = useState<any>(null)

  const getStatusBadge = (status: string, score: number, passScore: number) => {
    if (status === "passed") {
      return <Badge className="bg-green-100 text-green-800">通过</Badge>
    } else if (status === "failed") {
      return <Badge className="bg-red-100 text-red-800">未通过</Badge>
    }
    return <Badge variant="secondary">{status}</Badge>
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "passed":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "failed":
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
    }
  }

  const filteredSubmissions = submissions.filter((submission) => {
    const matchesSearch =
      submission.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      submission.examTitle.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStudent = selectedStudent === "全部学员" || submission.studentName === selectedStudent
    const matchesCategory = selectedCategory === "全部分类" || submission.category === selectedCategory
    const matchesStatus =
      selectedStatus === "全部状态" ||
      (selectedStatus === "通过" && submission.status === "passed") ||
      (selectedStatus === "未通过" && submission.status === "failed")

    return matchesSearch && matchesStudent && matchesCategory && matchesStatus
  })

  // 统计数据
  const totalSubmissions = submissions.length
  const passedSubmissions = submissions.filter((s) => s.status === "passed").length
  const failedSubmissions = submissions.filter((s) => s.status === "failed").length
  const averageScore = submissions.reduce((sum, s) => sum + s.score, 0) / submissions.length

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">已提交试卷</h2>
          <p className="text-muted-foreground">查看和管理学员的考试提交记录</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出记录
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索学员或考试..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>
        <Select value={selectedStudent} onValueChange={setSelectedStudent}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择学员" />
          </SelectTrigger>
          <SelectContent>
            {students.map((student) => (
              <SelectItem key={student} value={student}>
                {student}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择分类" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择状态" />
          </SelectTrigger>
          <SelectContent>
            {statuses.map((status) => (
              <SelectItem key={status} value={status}>
                {status}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总提交数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalSubmissions}</div>
            <p className="text-xs text-muted-foreground">+5 较昨日</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">通过数量</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{passedSubmissions}</div>
            <p className="text-xs text-muted-foreground">
              通过率 {Math.round((passedSubmissions / totalSubmissions) * 100)}%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">未通过数量</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{failedSubmissions}</div>
            <p className="text-xs text-muted-foreground">需要重考</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均分数</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(averageScore)}</div>
            <p className="text-xs text-muted-foreground">整体表现良好</p>
          </CardContent>
        </Card>
      </div>

      {/* 提交记录列表 */}
      <Card>
        <CardHeader>
          <CardTitle>考试提交记录</CardTitle>
          <CardDescription>显示所有学员的考试提交详情和成绩</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredSubmissions.map((submission) => (
              <div
                key={submission.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
              >
                <div className="flex items-center space-x-4">
                  {getStatusIcon(submission.status)}
                  <div>
                    <h4 className="font-medium">{submission.examTitle}</h4>
                    <p className="text-sm text-muted-foreground">{submission.category}</p>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="text-sm font-medium">{submission.studentName}</p>
                    <p className="text-xs text-muted-foreground">{submission.studentId}</p>
                  </div>

                  <div className="text-center">
                    <p className="text-lg font-bold text-blue-600">{submission.score}</p>
                    <p className="text-xs text-muted-foreground">/ {submission.totalScore}</p>
                  </div>

                  <div className="text-center">
                    <div className="flex items-center space-x-2 mb-1">
                      <Progress
                        value={(submission.correctAnswers / submission.totalQuestions) * 100}
                        className="w-16"
                      />
                      <span className="text-xs">
                        {submission.correctAnswers}/{submission.totalQuestions}
                      </span>
                    </div>
                    {getStatusBadge(submission.status, submission.score, submission.passScore)}
                  </div>

                  <div className="text-center">
                    <p className="text-sm">{submission.timeSpent}分钟</p>
                    <p className="text-xs text-muted-foreground">第{submission.attempt}次尝试</p>
                  </div>

                  <div className="text-center">
                    <p className="text-xs text-muted-foreground">{submission.submissionTime.split(" ")[0]}</p>
                    <p className="text-xs text-muted-foreground">{submission.submissionTime.split(" ")[1]}</p>
                  </div>

                  <div className="flex space-x-2">
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button size="sm" variant="outline" onClick={() => setSelectedSubmission(submission)}>
                          <Eye className="h-3 w-3" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-[600px]">
                        <DialogHeader>
                          <DialogTitle>考试详情</DialogTitle>
                          <DialogDescription>
                            {selectedSubmission?.studentName} - {selectedSubmission?.examTitle}
                          </DialogDescription>
                        </DialogHeader>
                        {selectedSubmission && (
                          <div className="space-y-4">
                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <h4 className="font-medium mb-2">基本信息</h4>
                                <div className="space-y-1 text-sm">
                                  <p>
                                    学员: {selectedSubmission.studentName} ({selectedSubmission.studentId})
                                  </p>
                                  <p>考试: {selectedSubmission.examTitle}</p>
                                  <p>分类: {selectedSubmission.category}</p>
                                  <p>提交时间: {selectedSubmission.submissionTime}</p>
                                </div>
                              </div>
                              <div>
                                <h4 className="font-medium mb-2">成绩详情</h4>
                                <div className="space-y-1 text-sm">
                                  <p>
                                    得分: {selectedSubmission.score} / {selectedSubmission.totalScore}
                                  </p>
                                  <p>及格分: {selectedSubmission.passScore}</p>
                                  <p>
                                    正确率:{" "}
                                    {Math.round(
                                      (selectedSubmission.correctAnswers / selectedSubmission.totalQuestions) * 100,
                                    )}
                                    %
                                  </p>
                                  <p>
                                    用时: {selectedSubmission.timeSpent} / {selectedSubmission.duration} 分钟
                                  </p>
                                </div>
                              </div>
                            </div>

                            <div>
                              <h4 className="font-medium mb-2">答题统计</h4>
                              <div className="grid grid-cols-3 gap-4 text-center">
                                <div className="p-3 bg-blue-50 rounded-lg">
                                  <p className="text-2xl font-bold text-blue-600">
                                    {selectedSubmission.totalQuestions}
                                  </p>
                                  <p className="text-xs text-muted-foreground">总题数</p>
                                </div>
                                <div className="p-3 bg-green-50 rounded-lg">
                                  <p className="text-2xl font-bold text-green-600">
                                    {selectedSubmission.correctAnswers}
                                  </p>
                                  <p className="text-xs text-muted-foreground">正确</p>
                                </div>
                                <div className="p-3 bg-red-50 rounded-lg">
                                  <p className="text-2xl font-bold text-red-600">
                                    {selectedSubmission.totalQuestions - selectedSubmission.correctAnswers}
                                  </p>
                                  <p className="text-xs text-muted-foreground">错误</p>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                      </DialogContent>
                    </Dialog>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredSubmissions.length === 0 && (
            <div className="flex flex-col items-center justify-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">暂无提交记录</h3>
              <p className="text-muted-foreground text-center">没有找到符合条件的考试提交记录，请尝试调整筛选条件。</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
