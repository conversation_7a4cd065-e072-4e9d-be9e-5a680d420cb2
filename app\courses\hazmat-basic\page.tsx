"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Clock, Users, BookOpen, Play, CheckCircle, FileText, Award, ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function HazmatBasicCoursePage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="sm" asChild>
              <Link href="/dashboard">
                <ArrowLeft className="h-4 w-4 mr-2" />
                返回仪表板
              </Link>
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">危化品基础培训</h1>
              <p className="text-gray-600">IMDG Code标准危险品处理培训课程</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Badge className="bg-red-100 text-red-700">危化品培训</Badge>
            <Button>编辑课程</Button>
          </div>
        </div>
      </header>

      <div className="p-6">
        {/* Course Overview */}
        <div className="grid gap-6 md:grid-cols-3 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">注册学员</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">156</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+12</span> 本月新增
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">完成率</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">85%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+5%</span> 较上月
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均分数</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">87.5</div>
              <p className="text-xs text-muted-foreground">满分100分</p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="content" className="space-y-6">
          <TabsList>
            <TabsTrigger value="content">课程内容</TabsTrigger>
            <TabsTrigger value="students">学员进度</TabsTrigger>
            <TabsTrigger value="materials">学习资料</TabsTrigger>
            <TabsTrigger value="assessments">考核评估</TabsTrigger>
          </TabsList>

          <TabsContent value="content" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Course Structure */}
              <Card>
                <CardHeader>
                  <CardTitle>课程结构</CardTitle>
                  <CardDescription>共8个模块，40学时</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {[
                    { title: "危化品分类与识别", duration: "5小时", completed: 142, total: 156 },
                    { title: "包装与标识要求", duration: "4小时", completed: 138, total: 156 },
                    { title: "装卸操作规程", duration: "6小时", completed: 125, total: 156 },
                    { title: "储存安全管理", duration: "5小时", completed: 118, total: 156 },
                    { title: "应急处理程序", duration: "6小时", completed: 95, total: 156 },
                    { title: "个人防护设备", duration: "4小时", completed: 89, total: 156 },
                    { title: "环境保护要求", duration: "5小时", completed: 76, total: 156 },
                    { title: "法规与标准", duration: "5小时", completed: 45, total: 156 },
                  ].map((module, index) => (
                    <div key={index} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Play className="h-4 w-4 text-blue-500" />
                          <span className="font-medium">{module.title}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-3 w-3 text-gray-400" />
                          <span className="text-sm text-gray-500">{module.duration}</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <Progress value={(module.completed / module.total) * 100} className="flex-1 mr-4" />
                        <span className="text-gray-500">
                          {module.completed}/{module.total}
                        </span>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Learning Objectives */}
              <Card>
                <CardHeader>
                  <CardTitle>学习目标</CardTitle>
                  <CardDescription>完成本课程后，学员将能够</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {[
                    "正确识别和分类各种危险化学品",
                    "掌握危化品的安全装卸操作程序",
                    "了解相关国际法规和标准要求",
                    "能够制定应急处理预案",
                    "正确使用个人防护设备",
                    "执行环境保护相关措施",
                  ].map((objective, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{objective}</span>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Course Requirements */}
            <Card>
              <CardHeader>
                <CardTitle>课程要求</CardTitle>
                <CardDescription>参加本课程的前提条件和要求</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-2">
                  <div>
                    <h4 className="font-medium mb-2">前提条件</h4>
                    <ul className="space-y-1 text-sm text-gray-600">
                      <li>• 持有有效的船员证书</li>
                      <li>• 具备基本的英语阅读能力</li>
                      <li>• 完成基础安全培训</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">认证要求</h4>
                    <ul className="space-y-1 text-sm text-gray-600">
                      <li>• 完成所有8个模块学习</li>
                      <li>• 通过在线考试（80分以上）</li>
                      <li>• 参加实操演练评估</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="students" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>学员进度跟踪</CardTitle>
                <CardDescription>查看所有注册学员的学习进度和成绩</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { name: "张三", company: "中远海运", progress: 95, score: 92, status: "即将完成" },
                    { name: "李四", company: "招商轮船", progress: 100, score: 88, status: "已完成" },
                    { name: "王五", company: "中海油运", progress: 65, score: 85, status: "进行中" },
                    { name: "赵六", company: "长航集团", progress: 45, score: 78, status: "进行中" },
                    { name: "孙七", company: "中远海运", progress: 30, score: 82, status: "进行中" },
                  ].map((student, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">{student.name.charAt(0)}</span>
                        </div>
                        <div>
                          <p className="font-medium">{student.name}</p>
                          <p className="text-sm text-gray-500">{student.company}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-6">
                        <div className="text-center">
                          <p className="text-sm font-medium">{student.progress}%</p>
                          <p className="text-xs text-gray-500">进度</p>
                        </div>
                        <div className="text-center">
                          <p className="text-sm font-medium">{student.score}</p>
                          <p className="text-xs text-gray-500">分数</p>
                        </div>
                        <div className="text-center">
                          <Badge
                            variant={student.status === "已完成" ? "default" : "secondary"}
                            className={student.status === "已完成" ? "bg-green-100 text-green-700" : ""}
                          >
                            {student.status}
                          </Badge>
                        </div>
                        <Button size="sm" variant="outline">
                          详情
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="materials" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>学习资料</CardTitle>
                  <CardDescription>课程相关的文档和资源</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {[
                    { name: "IMDG Code 2022版", type: "PDF", size: "15.2 MB" },
                    { name: "危化品分类图表", type: "PDF", size: "3.8 MB" },
                    { name: "应急处理手册", type: "PDF", size: "8.5 MB" },
                    { name: "安全操作视频", type: "MP4", size: "125 MB" },
                    { name: "个人防护设备指南", type: "PDF", size: "4.2 MB" },
                  ].map((material, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center space-x-3">
                        <FileText className="h-5 w-5 text-blue-500" />
                        <div>
                          <p className="font-medium">{material.name}</p>
                          <p className="text-sm text-gray-500">
                            {material.type} • {material.size}
                          </p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline">
                        下载
                      </Button>
                    </div>
                  ))}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>参考资料</CardTitle>
                  <CardDescription>额外的学习参考和标准文档</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {[
                    { name: "IMO危化品运输指南", type: "外部链接" },
                    { name: "国际海事法规汇编", type: "外部链接" },
                    { name: "船舶污染防治公约", type: "外部链接" },
                    { name: "危化品事故案例分析", type: "外部链接" },
                    { name: "最新法规更新通知", type: "外部链接" },
                  ].map((reference, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded">
                      <div className="flex items-center space-x-3">
                        <BookOpen className="h-5 w-5 text-green-500" />
                        <div>
                          <p className="font-medium">{reference.name}</p>
                          <p className="text-sm text-gray-500">{reference.type}</p>
                        </div>
                      </div>
                      <Button size="sm" variant="outline">
                        访问
                      </Button>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="assessments" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              <Card>
                <CardHeader>
                  <CardTitle>在线考试</CardTitle>
                  <CardDescription>理论知识考核和评估</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">模块1-4综合测试</p>
                      <p className="text-sm text-gray-500">50题 • 90分钟 • 通过分数: 80分</p>
                    </div>
                    <Badge className="bg-green-100 text-green-700">已发布</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">模块5-8综合测试</p>
                      <p className="text-sm text-gray-500">50题 • 90分钟 • 通过分数: 80分</p>
                    </div>
                    <Badge variant="secondary">准备中</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">最终综合考试</p>
                      <p className="text-sm text-gray-500">100题 • 180分钟 • 通过分数: 80分</p>
                    </div>
                    <Badge variant="secondary">准备中</Badge>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>实操评估</CardTitle>
                  <CardDescription>实际操作技能考核</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">危化品识别与分类</p>
                      <p className="text-sm text-gray-500">现场操作 • 30分钟</p>
                    </div>
                    <Badge className="bg-blue-100 text-blue-700">计划中</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">应急处理演练</p>
                      <p className="text-sm text-gray-500">模拟演练 • 45分钟</p>
                    </div>
                    <Badge className="bg-blue-100 text-blue-700">计划中</Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div>
                      <p className="font-medium">防护设备使用</p>
                      <p className="text-sm text-gray-500">实际操作 • 20分钟</p>
                    </div>
                    <Badge className="bg-blue-100 text-blue-700">计划中</Badge>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Assessment Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>考核统计</CardTitle>
                <CardDescription>学员考试成绩和通过率统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">85%</div>
                    <p className="text-sm text-gray-500">总体通过率</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">87.5</div>
                    <p className="text-sm text-gray-500">平均分数</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-orange-600">132</div>
                    <p className="text-sm text-gray-500">已完成考试</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-red-600">24</div>
                    <p className="text-sm text-gray-500">待补考</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
