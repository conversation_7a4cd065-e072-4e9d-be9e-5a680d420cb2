"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Users, BookOpen, Award, TrendingUp, AlertTriangle, CheckCircle, Ship, Shield, Globe } from "lucide-react"
import Link from "next/link"

export default function DashboardPage() {
  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">培训管理仪表板</h1>
            <p className="text-gray-600">欢迎回来，船长张三</p>
          </div>
          <div className="flex items-center gap-4">
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <CheckCircle className="w-3 h-3 mr-1" />
              系统正常
            </Badge>
            <Button asChild>
              <Link href="/courses/new">创建新课程</Link>
            </Button>
          </div>
        </div>
      </header>

      <div className="flex-1 p-6">
        {/* Stats Cards */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总学员数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,247</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+12%</span> 较上月
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">活跃课程</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">24</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+3</span> 本月新增
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">已颁发证书</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">892</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+18%</span> 较上月
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">完成率</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">94.2%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+2.1%</span> 较上月
              </p>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList>
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="courses">课程管理</TabsTrigger>
            <TabsTrigger value="students">学员管理</TabsTrigger>
            <TabsTrigger value="certificates">证书管理</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Recent Activity */}
              <Card>
                <CardHeader>
                  <CardTitle>最近活动</CardTitle>
                  <CardDescription>系统最新动态和重要事件</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">李四完成了危化品基础培训</p>
                      <p className="text-xs text-gray-500">2小时前</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">新增油品操作高级课程</p>
                      <p className="text-xs text-gray-500">4小时前</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">王五的STCW证书即将到期</p>
                      <p className="text-xs text-gray-500">6小时前</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Upcoming Deadlines */}
              <Card>
                <CardHeader>
                  <CardTitle>即将到期的证书</CardTitle>
                  <CardDescription>需要关注的证书续期提醒</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <AlertTriangle className="h-4 w-4 text-orange-500" />
                      <div>
                        <p className="text-sm font-medium">张三 - 危化品操作证</p>
                        <p className="text-xs text-gray-500">7天后到期</p>
                      </div>
                    </div>
                    <Button size="sm" variant="outline">
                      提醒
                    </Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <AlertTriangle className="h-4 w-4 text-red-500" />
                      <div>
                        <p className="text-sm font-medium">李四 - 油品装卸证</p>
                        <p className="text-xs text-gray-500">3天后到期</p>
                      </div>
                    </div>
                    <Button size="sm" variant="outline">
                      提醒
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Training Progress */}
            <Card>
              <CardHeader>
                <CardTitle>培训进度概览</CardTitle>
                <CardDescription>各类培训课程的完成情况</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">危化品基础培训</span>
                    <span className="text-sm text-gray-500">85%</span>
                  </div>
                  <Progress value={85} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">油品操作认证</span>
                    <span className="text-sm text-gray-500">72%</span>
                  </div>
                  <Progress value={72} className="h-2" />
                </div>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">STCW基本安全培训</span>
                    <span className="text-sm text-gray-500">91%</span>
                  </div>
                  <Progress value={91} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="courses" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Shield className="h-8 w-8 text-red-500" />
                    <Badge className="bg-red-100 text-red-700">危化品</Badge>
                  </div>
                  <CardTitle>危化品基础培训</CardTitle>
                  <CardDescription>IMDG Code标准危险品处理培训</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>已注册学员</span>
                      <span className="font-medium">156人</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>完成率</span>
                      <span className="font-medium">85%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>课程时长</span>
                      <span className="font-medium">40小时</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline" asChild>
                    <Link href="/courses/hazmat-basic">查看详情</Link>
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Globe className="h-8 w-8 text-blue-500" />
                    <Badge className="bg-blue-100 text-blue-700">油品</Badge>
                  </div>
                  <CardTitle>油品操作认证</CardTitle>
                  <CardDescription>石油产品装卸和污染防控培训</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>已注册学员</span>
                      <span className="font-medium">89人</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>完成率</span>
                      <span className="font-medium">72%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>课程时长</span>
                      <span className="font-medium">60小时</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline" asChild>
                    <Link href="/courses/oil-operations">查看详情</Link>
                  </Button>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <Ship className="h-8 w-8 text-green-500" />
                    <Badge className="bg-green-100 text-green-700">STCW</Badge>
                  </div>
                  <CardTitle>基本安全培训</CardTitle>
                  <CardDescription>STCW公约基本安全培训课程</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>已注册学员</span>
                      <span className="font-medium">234人</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>完成率</span>
                      <span className="font-medium">91%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>课程时长</span>
                      <span className="font-medium">32小时</span>
                    </div>
                  </div>
                  <Button className="w-full mt-4 bg-transparent" variant="outline" asChild>
                    <Link href="/courses/stcw-basic">查看详情</Link>
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="students" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>学员管理</CardTitle>
                <CardDescription>管理和跟踪所有注册学员的培训进度</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    { name: "张三", company: "中远海运", progress: 85, status: "进行中" },
                    { name: "李四", company: "招商轮船", progress: 100, status: "已完成" },
                    { name: "王五", company: "中海油运", progress: 45, status: "进行中" },
                    { name: "赵六", company: "长航集团", progress: 78, status: "进行中" },
                  ].map((student, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">{student.name.charAt(0)}</span>
                        </div>
                        <div>
                          <p className="font-medium">{student.name}</p>
                          <p className="text-sm text-gray-500">{student.company}</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <p className="text-sm font-medium">{student.progress}%</p>
                          <Badge variant={student.status === "已完成" ? "default" : "secondary"}>
                            {student.status}
                          </Badge>
                        </div>
                        <Button size="sm" variant="outline">
                          查看
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="certificates" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>证书管理</CardTitle>
                <CardDescription>管理已颁发的证书和即将到期的认证</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {[
                    {
                      student: "张三",
                      certificate: "危化品操作证",
                      issued: "2024-01-15",
                      expires: "2025-01-15",
                      status: "有效",
                    },
                    {
                      student: "李四",
                      certificate: "油品装卸证",
                      issued: "2023-12-20",
                      expires: "2024-12-20",
                      status: "即将到期",
                    },
                    {
                      student: "王五",
                      certificate: "STCW基本安全",
                      issued: "2024-02-10",
                      expires: "2026-02-10",
                      status: "有效",
                    },
                  ].map((cert, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <Award className="h-8 w-8 text-yellow-500" />
                        <div>
                          <p className="font-medium">{cert.certificate}</p>
                          <p className="text-sm text-gray-500">持证人: {cert.student}</p>
                          <p className="text-xs text-gray-400">
                            颁发: {cert.issued} | 到期: {cert.expires}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <Badge
                          variant={cert.status === "有效" ? "default" : "destructive"}
                          className={cert.status === "有效" ? "bg-green-100 text-green-700" : ""}
                        >
                          {cert.status}
                        </Badge>
                        <Button size="sm" variant="outline">
                          下载
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
