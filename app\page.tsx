"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { SidebarTrigger } from "@/components/ui/sidebar"
import {
  Users,
  BookOpen,
  Award,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Brain,
  Calendar,
  Activity,
  DollarSign,
  FileText,
  MapPin,
} from "lucide-react"
import {
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  Pie<PERSON>hart,
  Pie,
  Cell,
  AreaChart,
  Area,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

// 更丰富的数据集
const trainingProgressData = [
  { month: "1月", completed: 45, inProgress: 23, planned: 12, budget: 120000, satisfaction: 4.2 },
  { month: "2月", completed: 52, inProgress: 28, planned: 15, budget: 135000, satisfaction: 4.3 },
  { month: "3月", completed: 48, inProgress: 31, planned: 18, budget: 142000, satisfaction: 4.1 },
  { month: "4月", completed: 61, inProgress: 25, planned: 14, budget: 128000, satisfaction: 4.4 },
  { month: "5月", completed: 55, inProgress: 33, planned: 22, budget: 156000, satisfaction: 4.2 },
  { month: "6月", completed: 67, inProgress: 29, planned: 16, budget: 148000, satisfaction: 4.5 },
  { month: "7月", completed: 72, inProgress: 35, planned: 20, budget: 162000, satisfaction: 4.3 },
  { month: "8月", completed: 68, inProgress: 31, planned: 18, budget: 145000, satisfaction: 4.4 },
]

const certificationData = [
  { name: "化学品船", value: 45, color: "#ef4444", growth: 12 },
  { name: "油轮", value: 38, color: "#3b82f6", growth: 8 },
  { name: "STCW基础", value: 67, color: "#10b981", growth: 15 },
  { name: "高级安全", value: 23, color: "#f59e0b", growth: 5 },
  { name: "应急响应", value: 31, color: "#8b5cf6", growth: 9 },
]

const performanceRadarData = [
  { subject: "理论知识", A: 85, B: 78, fullMark: 100 },
  { subject: "实操技能", A: 92, B: 85, fullMark: 100 },
  { subject: "安全意识", A: 88, B: 82, fullMark: 100 },
  { subject: "应急处理", A: 76, B: 88, fullMark: 100 },
  { subject: "团队协作", A: 90, B: 75, fullMark: 100 },
  { subject: "沟通能力", A: 82, B: 90, fullMark: 100 },
]

const trainingEfficiencyData = [
  { name: "化学品船", efficiency: 85, cost: 12000, participants: 45 },
  { name: "油轮操作", efficiency: 78, cost: 15000, participants: 38 },
  { name: "STCW基础", efficiency: 92, cost: 8000, participants: 67 },
  { name: "应急响应", efficiency: 76, cost: 18000, participants: 23 },
  { name: "高级安全", efficiency: 88, cost: 22000, participants: 31 },
]

const dailyActivityData = [
  { time: "00:00", logins: 12, activities: 8, completions: 3 },
  { time: "04:00", logins: 8, activities: 5, completions: 2 },
  { time: "08:00", logins: 45, activities: 38, completions: 12 },
  { time: "12:00", logins: 67, activities: 52, completions: 18 },
  { time: "16:00", logins: 89, activities: 71, completions: 25 },
  { time: "20:00", logins: 56, activities: 43, completions: 15 },
]

const regionPerformanceData = [
  { region: "华东", score: 92, participants: 156, completion: 94 },
  { region: "华南", score: 88, participants: 134, completion: 91 },
  { region: "华北", score: 85, participants: 98, completion: 87 },
  { region: "西南", score: 79, participants: 76, completion: 82 },
  { region: "东北", score: 83, participants: 89, completion: 85 },
]

const upcomingExpirations = [
  { name: "张三", certificate: "化学品船操作", expires: "2024-02-15", daysLeft: 7, risk: "高" },
  { name: "李四", certificate: "油轮安全", expires: "2024-02-20", daysLeft: 12, risk: "中" },
  { name: "王五", certificate: "STCW基本安全", expires: "2024-02-25", daysLeft: 17, risk: "中" },
  { name: "赵六", certificate: "高级消防", expires: "2024-03-01", daysLeft: 22, risk: "低" },
  { name: "孙七", certificate: "应急响应", expires: "2024-03-05", daysLeft: 26, risk: "低" },
]

const instructorPerformance = [
  { name: "张教官", rating: 4.8, students: 45, courses: 3, efficiency: 92 },
  { name: "李专家", rating: 4.6, students: 38, courses: 2, efficiency: 88 },
  { name: "王船长", rating: 4.9, students: 67, courses: 4, efficiency: 95 },
  { name: "赵主任", rating: 4.4, students: 23, courses: 1, efficiency: 85 },
]

export default function DashboardPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header with Sidebar Trigger */}
      <header className="bg-white border-b px-6 py-4 sticky top-0 z-10">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <SidebarTrigger />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">培训仪表板</h1>
              <p className="text-gray-600">海事船员培训管理系统</p>
            </div>
          </div>
          <div className="flex items-center gap-4">
            <Select defaultValue="all-regions">
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all-regions">全部地区</SelectItem>
                <SelectItem value="east">华东</SelectItem>
                <SelectItem value="south">华南</SelectItem>
                <SelectItem value="north">华北</SelectItem>
                <SelectItem value="southwest">西南</SelectItem>
                <SelectItem value="northeast">东北</SelectItem>
              </SelectContent>
            </Select>
            <Select defaultValue="this-month">
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">今天</SelectItem>
                <SelectItem value="this-week">本周</SelectItem>
                <SelectItem value="this-month">本月</SelectItem>
                <SelectItem value="this-quarter">本季度</SelectItem>
                <SelectItem value="this-year">今年</SelectItem>
              </SelectContent>
            </Select>
            <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
              <CheckCircle className="w-3 h-3 mr-1" />
              系统正常
            </Badge>
            <Button>生成报告</Button>
          </div>
        </div>
      </header>

      <div className="flex-1 p-6 space-y-6 overflow-auto">
        {/* Enhanced KPI Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">船员总数</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,247</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+12%</span> 较上月
              </p>
              <div className="mt-2">
                <Progress value={78} className="h-1" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">活跃培训</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">24</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+3</span> 本月新增
              </p>
              <div className="mt-2">
                <Progress value={65} className="h-1" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">证书颁发</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">892</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+18%</span> 较上月
              </p>
              <div className="mt-2">
                <Progress value={89} className="h-1" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">完成率</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">94.2%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+2.1%</span> 较上月
              </p>
              <div className="mt-2">
                <Progress value={94} className="h-1" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">培训预算</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">¥2.3M</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-orange-600">85%</span> 已使用
              </p>
              <div className="mt-2">
                <Progress value={85} className="h-1" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">满意度</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">4.3</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+0.2</span> 较上月
              </p>
              <div className="mt-2">
                <Progress value={86} className="h-1" />
              </div>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="overview" className="space-y-6">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="performance">绩效分析</TabsTrigger>
            <TabsTrigger value="regional">地区分析</TabsTrigger>
            <TabsTrigger value="realtime">实时监控</TabsTrigger>
            <TabsTrigger value="certifications">证书管理</TabsTrigger>
            <TabsTrigger value="ai-insights">AI洞察</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {/* Enhanced Training Progress Chart */}
              <Card className="lg:col-span-2">
                <CardHeader>
                  <CardTitle>培训进度趋势</CardTitle>
                  <CardDescription>月度培训完成统计与预算分析</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={350}>
                    <ComposedChart data={trainingProgressData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Bar yAxisId="left" dataKey="completed" fill="#10b981" name="已完成" />
                      <Bar yAxisId="left" dataKey="inProgress" fill="#3b82f6" name="进行中" />
                      <Bar yAxisId="left" dataKey="planned" fill="#f59e0b" name="计划中" />
                      <Line
                        yAxisId="right"
                        type="monotone"
                        dataKey="satisfaction"
                        stroke="#ef4444"
                        strokeWidth={3}
                        name="满意度"
                      />
                    </ComposedChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Recent Activity with more details */}
              <Card>
                <CardHeader>
                  <CardTitle>最近活动</CardTitle>
                  <CardDescription>系统最新动态和重要事件</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 max-h-[350px] overflow-y-auto">
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">张三完成了化学品船安全课程</p>
                      <p className="text-xs text-gray-500">2小时前 • 上海培训中心</p>
                      <Badge className="text-xs bg-green-100 text-green-700">优秀</Badge>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">AI为油轮考试新增生成题目</p>
                      <p className="text-xs text-gray-500">4小时前 • 自动生成25题</p>
                      <Badge className="text-xs bg-purple-100 text-purple-700">AI生成</Badge>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">李四的STCW证书将在7天后到期</p>
                      <p className="text-xs text-gray-500">6小时前 • 需要续期</p>
                      <Badge className="text-xs bg-orange-100 text-orange-700">紧急</Badge>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">"高级安全"培训阶段为15名船员开始</p>
                      <p className="text-xs text-gray-500">1天前 • 青岛培训基地</p>
                      <Badge className="text-xs bg-blue-100 text-blue-700">新阶段</Badge>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">王五考试未通过，需要重新培训</p>
                      <p className="text-xs text-gray-500">2天前 • 化学品船操作</p>
                      <Badge className="text-xs bg-red-100 text-red-700">需关注</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              {/* Training Efficiency Scatter Plot */}
              <Card>
                <CardHeader>
                  <CardTitle>培训效率分析</CardTitle>
                  <CardDescription>成本效益与参与人数关系</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <ScatterChart data={trainingEfficiencyData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="cost" name="成本" />
                      <YAxis dataKey="efficiency" name="效率" />
                      <ZAxis dataKey="participants" range={[50, 400]} name="参与人数" />
                      <Tooltip cursor={{ strokeDasharray: "3 3" }} />
                      <Scatter dataKey="efficiency" fill="#3b82f6" />
                    </ScatterChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Daily Activity Pattern */}
              <Card>
                <CardHeader>
                  <CardTitle>日活跃度模式</CardTitle>
                  <CardDescription>24小时用户活动分布</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <AreaChart data={dailyActivityData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="time" />
                      <YAxis />
                      <Tooltip />
                      <Area
                        type="monotone"
                        dataKey="logins"
                        stackId="1"
                        stroke="#3b82f6"
                        fill="#3b82f6"
                        fillOpacity={0.6}
                        name="登录"
                      />
                      <Area
                        type="monotone"
                        dataKey="activities"
                        stackId="1"
                        stroke="#10b981"
                        fill="#10b981"
                        fillOpacity={0.6}
                        name="活动"
                      />
                      <Area
                        type="monotone"
                        dataKey="completions"
                        stackId="1"
                        stroke="#f59e0b"
                        fill="#f59e0b"
                        fillOpacity={0.6}
                        name="完成"
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Enhanced Certificate Expirations */}
            <Card>
              <CardHeader>
                <CardTitle>即将到期的证书</CardTitle>
                <CardDescription>需要续期关注的证书详细信息</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingExpirations.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <AlertTriangle
                          className={`h-4 w-4 ${
                            item.risk === "高"
                              ? "text-red-500"
                              : item.risk === "中"
                                ? "text-orange-500"
                                : "text-yellow-500"
                          }`}
                        />
                        <div>
                          <p className="text-sm font-medium">
                            {item.name} - {item.certificate}
                          </p>
                          <p className="text-xs text-gray-500">
                            到期时间: {item.expires} (还有{item.daysLeft}天)
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge
                          className={
                            item.risk === "高"
                              ? "bg-red-100 text-red-700"
                              : item.risk === "中"
                                ? "bg-orange-100 text-orange-700"
                                : "bg-yellow-100 text-yellow-700"
                          }
                        >
                          {item.risk}风险
                        </Badge>
                        <Button size="sm" variant="outline">
                          发送提醒
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="performance" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Performance Radar Chart */}
              <Card>
                <CardHeader>
                  <CardTitle>综合能力雷达图</CardTitle>
                  <CardDescription>化学品船 vs 油轮操作能力对比</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={350}>
                    <RadarChart data={performanceRadarData}>
                      <PolarGrid />
                      <PolarAngleAxis dataKey="subject" />
                      <PolarRadiusAxis angle={90} domain={[0, 100]} />
                      <Radar name="化学品船" dataKey="A" stroke="#ef4444" fill="#ef4444" fillOpacity={0.3} />
                      <Radar name="油轮操作" dataKey="B" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.3} />
                      <Tooltip />
                    </RadarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Instructor Performance */}
              <Card>
                <CardHeader>
                  <CardTitle>教官绩效排名</CardTitle>
                  <CardDescription>基于评分、学员数量和效率的综合排名</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {instructorPerformance.map((instructor, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                          </div>
                          <div>
                            <p className="font-medium">{instructor.name}</p>
                            <p className="text-sm text-gray-500">
                              {instructor.students}名学员 • {instructor.courses}门课程
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">{instructor.rating}⭐</span>
                            <Badge className="bg-green-100 text-green-700">{instructor.efficiency}%</Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Training Categories Performance */}
            <Card>
              <CardHeader>
                <CardTitle>培训类别表现分析</CardTitle>
                <CardDescription>各培训类别的完成率、增长率和参与度</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-5">
                  {certificationData.map((category, index) => (
                    <div key={index} className="text-center p-4 border rounded-lg">
                      <div
                        className="w-16 h-16 mx-auto mb-3 rounded-full flex items-center justify-center"
                        style={{ backgroundColor: category.color + "20" }}
                      >
                        <div className="w-8 h-8 rounded-full" style={{ backgroundColor: category.color }}></div>
                      </div>
                      <h3 className="font-medium">{category.name}</h3>
                      <p className="text-2xl font-bold mt-1">{category.value}</p>
                      <p className="text-sm text-gray-500">证书数量</p>
                      <Badge className="mt-2 bg-green-100 text-green-700">+{category.growth}%</Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="regional" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Regional Performance */}
              <Card>
                <CardHeader>
                  <CardTitle>地区绩效对比</CardTitle>
                  <CardDescription>各地区培训质量和完成率</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={350}>
                    <BarChart data={regionPerformanceData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="region" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="score" fill="#3b82f6" name="平均分数" />
                      <Bar dataKey="completion" fill="#10b981" name="完成率" />
                    </BarChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Regional Details */}
              <Card>
                <CardHeader>
                  <CardTitle>地区详细信息</CardTitle>
                  <CardDescription>各地区培训中心详情</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {regionPerformanceData.map((region, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <div>
                          <p className="font-medium">{region.region}</p>
                          <p className="text-sm text-gray-500">{region.participants}名参与者</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">分数: {region.score}</p>
                        <p className="text-sm text-gray-500">完成: {region.completion}%</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="realtime" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-3">
              {/* Real-time Stats */}
              <Card>
                <CardHeader>
                  <CardTitle>实时统计</CardTitle>
                  <CardDescription>当前系统活动状态</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-sm">在线用户</span>
                    </div>
                    <span className="font-bold">156</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                      <span className="text-sm">进行中考试</span>
                    </div>
                    <span className="font-bold">23</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse"></div>
                      <span className="text-sm">待审核</span>
                    </div>
                    <span className="font-bold">8</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
                      <span className="text-sm">AI处理中</span>
                    </div>
                    <span className="font-bold">12</span>
                  </div>
                </CardContent>
              </Card>

              {/* System Health */}
              <Card>
                <CardHeader>
                  <CardTitle>系统健康度</CardTitle>
                  <CardDescription>各模块运行状态</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">数据库</span>
                    <Badge className="bg-green-100 text-green-700">正常</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">AI服务</span>
                    <Badge className="bg-green-100 text-green-700">正常</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">文件存储</span>
                    <Badge className="bg-yellow-100 text-yellow-700">警告</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">邮件服务</span>
                    <Badge className="bg-green-100 text-green-700">正常</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">备份系统</span>
                    <Badge className="bg-green-100 text-green-700">正常</Badge>
                  </div>
                </CardContent>
              </Card>

              {/* Quick Actions */}
              <Card>
                <CardHeader>
                  <CardTitle>快速操作</CardTitle>
                  <CardDescription>常用管理功能</CardDescription>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button className="w-full justify-start bg-transparent" variant="outline">
                    <Users className="w-4 h-4 mr-2" />
                    添加新船员
                  </Button>
                  <Button className="w-full justify-start bg-transparent" variant="outline">
                    <Calendar className="w-4 h-4 mr-2" />
                    创建培训阶段
                  </Button>
                  <Button className="w-full justify-start bg-transparent" variant="outline">
                    <Brain className="w-4 h-4 mr-2" />
                    AI生成题目
                  </Button>
                  <Button className="w-full justify-start bg-transparent" variant="outline">
                    <Award className="w-4 h-4 mr-2" />
                    颁发证书
                  </Button>
                  <Button className="w-full justify-start bg-transparent" variant="outline">
                    <FileText className="w-4 h-4 mr-2" />
                    导出报告
                  </Button>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="certifications" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Certification Distribution */}
              <Card>
                <CardHeader>
                  <CardTitle>证书分布</CardTitle>
                  <CardDescription>按类型的当前有效证书</CardDescription>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={certificationData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="value"
                        label={({ name, value }) => `${name}: ${value}`}
                      >
                        {certificationData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              {/* Certification Status */}
              <Card>
                <CardHeader>
                  <CardTitle>证书状态概览</CardTitle>
                  <CardDescription>所有证书的当前状态</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center space-x-3">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <div>
                        <p className="font-medium">有效证书</p>
                        <p className="text-sm text-gray-500">当前活跃且有效</p>
                      </div>
                    </div>
                    <span className="text-2xl font-bold text-green-600">173</span>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center space-x-3">
                      <Clock className="h-5 w-5 text-orange-500" />
                      <div>
                        <p className="font-medium">即将到期</p>
                        <p className="text-sm text-gray-500">30天内</p>
                      </div>
                    </div>
                    <span className="text-2xl font-bold text-orange-600">24</span>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center space-x-3">
                      <AlertTriangle className="h-5 w-5 text-red-500" />
                      <div>
                        <p className="font-medium">已过期</p>
                        <p className="text-sm text-gray-500">需要立即续期</p>
                      </div>
                    </div>
                    <span className="text-2xl font-bold text-red-600">8</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="ai-insights" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* AI Performance Insights */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="h-5 w-5 text-purple-500" />
                    AI性能洞察
                  </CardTitle>
                  <CardDescription>AI驱动的培训效果分析</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900">学习路径优化</h4>
                    <p className="text-sm text-blue-700 mt-1">
                      AI发现，当船员在油轮操作前完成化学品船安全培训时，表现提高23%。
                    </p>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900">题目难度分析</h4>
                    <p className="text-sm text-green-700 mt-1">
                      在应急响应培训中，AI生成的题目比传统题目的参与度高15%。
                    </p>
                  </div>
                  <div className="p-4 bg-orange-50 rounded-lg">
                    <h4 className="font-medium text-orange-900">性能预测</h4>
                    <p className="text-sm text-orange-700 mt-1">基于当前进度，12名船员有无法按时完成认证的风险。</p>
                  </div>
                </CardContent>
              </Card>

              {/* AI Recommendations */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-500" />
                    AI建议
                  </CardTitle>
                  <CardDescription>个性化改进建议</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium">增加实践练习</p>
                        <p className="text-xs text-gray-500">为化学品船培训增加2个实操环节以提高记忆效果</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium">优化题目难度</p>
                        <p className="text-xs text-gray-500">将模块3题目复杂度降低15%以提高通过率</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium">个性化学习路径</p>
                        <p className="text-xs text-gray-500">基于学习模式为8名船员创建定制路径</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3">
                      <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
                      <div>
                        <p className="text-sm font-medium">时间安排优化</p>
                        <p className="text-xs text-gray-500">重新安排3个培训课程以避免与操作任务冲突</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* AI Usage Statistics */}
            <Card>
              <CardHeader>
                <CardTitle>AI功能使用情况</CardTitle>
                <CardDescription>AI功能在平台中的使用统计</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">1,247</div>
                    <p className="text-sm text-gray-500">AI生成题目</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">89</div>
                    <p className="text-sm text-gray-500">个性化学习路径</p>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">156</div>
                    <p className="text-sm text-gray-500">自动反馈报告</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
