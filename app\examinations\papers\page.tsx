"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Search,
  Plus,
  FileText,
  Edit,
  Trash2,
  Eye,
  Copy,
  Download,
  Clock,
  Users,
  CheckCircle,
  AlertCircle,
} from "lucide-react"

// 模拟数据
const examPapers = [
  {
    id: 1,
    title: "船舶安全操作基础考试",
    category: "安全培训",
    difficulty: "初级",
    questionCount: 50,
    duration: 90,
    totalScore: 100,
    passScore: 60,
    status: "published",
    createdDate: "2024-01-15",
    createdBy: "张教官",
    attempts: 45,
    averageScore: 78,
    passRate: 82,
  },
  {
    id: 2,
    title: "海上求生技能测试",
    category: "求生技能",
    difficulty: "中级",
    questionCount: 40,
    duration: 75,
    totalScore: 100,
    passScore: 70,
    status: "draft",
    createdDate: "2024-01-12",
    createdBy: "李教官",
    attempts: 0,
    averageScore: 0,
    passRate: 0,
  },
  {
    id: 3,
    title: "消防安全综合评估",
    category: "消防安全",
    difficulty: "高级",
    questionCount: 60,
    duration: 120,
    totalScore: 100,
    passScore: 75,
    status: "published",
    createdDate: "2024-01-10",
    createdBy: "王教官",
    attempts: 28,
    averageScore: 85,
    passRate: 89,
  },
  {
    id: 4,
    title: "国际海事法规考核",
    category: "法规培训",
    difficulty: "中级",
    questionCount: 45,
    duration: 100,
    totalScore: 100,
    passScore: 65,
    status: "archived",
    createdDate: "2024-01-08",
    createdBy: "陈教官",
    attempts: 67,
    averageScore: 72,
    passRate: 76,
  },
]

const categories = ["全部分类", "安全培训", "求生技能", "消防安全", "法规培训"]
const difficulties = ["全部难度", "初级", "中级", "高级"]
const statuses = ["全部状态", "已发布", "草稿", "已归档"]

export default function ExamPapersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("全部分类")
  const [selectedDifficulty, setSelectedDifficulty] = useState("全部难度")
  const [selectedStatus, setSelectedStatus] = useState("全部状态")
  const [isCreateOpen, setIsCreateOpen] = useState(false)

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "published":
        return <Badge className="bg-green-100 text-green-800">已发布</Badge>
      case "draft":
        return <Badge className="bg-yellow-100 text-yellow-800">草稿</Badge>
      case "archived":
        return <Badge className="bg-gray-100 text-gray-800">已归档</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "初级":
        return "bg-green-100 text-green-800"
      case "中级":
        return "bg-yellow-100 text-yellow-800"
      case "高级":
        return "bg-red-100 text-red-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  const filteredPapers = examPapers.filter((paper) => {
    const matchesSearch = paper.title.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "全部分类" || paper.category === selectedCategory
    const matchesDifficulty = selectedDifficulty === "全部难度" || paper.difficulty === selectedDifficulty
    const matchesStatus =
      selectedStatus === "全部状态" ||
      (selectedStatus === "已发布" && paper.status === "published") ||
      (selectedStatus === "草稿" && paper.status === "draft") ||
      (selectedStatus === "已归档" && paper.status === "archived")

    return matchesSearch && matchesCategory && matchesDifficulty && matchesStatus
  })

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">试卷管理</h2>
          <p className="text-muted-foreground">创建、编辑和管理考试试卷</p>
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={isCreateOpen} onOpenChange={setIsCreateOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                创建试卷
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>创建新试卷</DialogTitle>
                <DialogDescription>填写试卷基本信息和配置</DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="title" className="text-right">
                    试卷标题
                  </Label>
                  <Input id="title" className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="category" className="text-right">
                    考试分类
                  </Label>
                  <Select>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="选择分类" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="safety">安全培训</SelectItem>
                      <SelectItem value="survival">求生技能</SelectItem>
                      <SelectItem value="fire">消防安全</SelectItem>
                      <SelectItem value="regulation">法规培训</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="difficulty" className="text-right">
                    难度等级
                  </Label>
                  <Select>
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="选择难度" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="beginner">初级</SelectItem>
                      <SelectItem value="intermediate">中级</SelectItem>
                      <SelectItem value="advanced">高级</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="duration" className="text-right">
                    考试时长
                  </Label>
                  <Input id="duration" type="number" placeholder="分钟" className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="passScore" className="text-right">
                    及格分数
                  </Label>
                  <Input id="passScore" type="number" placeholder="分数" className="col-span-3" />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="description" className="text-right">
                    试卷描述
                  </Label>
                  <Textarea id="description" className="col-span-3" />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit">创建试卷</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索试卷..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择分类" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择难度" />
          </SelectTrigger>
          <SelectContent>
            {difficulties.map((difficulty) => (
              <SelectItem key={difficulty} value={difficulty}>
                {difficulty}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择状态" />
          </SelectTrigger>
          <SelectContent>
            {statuses.map((status) => (
              <SelectItem key={status} value={status}>
                {status}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总试卷数</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{examPapers.length}</div>
            <p className="text-xs text-muted-foreground">+2 较上月</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已发布</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{examPapers.filter((p) => p.status === "published").length}</div>
            <p className="text-xs text-muted-foreground">可用于考试</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总参考人次</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{examPapers.reduce((sum, p) => sum + p.attempts, 0)}</div>
            <p className="text-xs text-muted-foreground">累计考试次数</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均通过率</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(
                examPapers.filter((p) => p.attempts > 0).reduce((sum, p) => sum + p.passRate, 0) /
                  examPapers.filter((p) => p.attempts > 0).length,
              )}
              %
            </div>
            <p className="text-xs text-muted-foreground">整体表现良好</p>
          </CardContent>
        </Card>
      </div>

      {/* 试卷列表 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {filteredPapers.map((paper) => (
          <Card key={paper.id} className="hover:shadow-md transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <CardTitle className="text-lg">{paper.title}</CardTitle>
                {getStatusBadge(paper.status)}
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">{paper.category}</Badge>
                <Badge className={getDifficultyColor(paper.difficulty)}>{paper.difficulty}</Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center space-x-1">
                    <FileText className="h-3 w-3" />
                    <span>{paper.questionCount} 题</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>{paper.duration} 分钟</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Users className="h-3 w-3" />
                    <span>{paper.attempts} 人次</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="h-3 w-3" />
                    <span>{paper.passRate}% 通过</span>
                  </div>
                </div>

                <div className="text-sm text-muted-foreground">
                  <p>
                    总分: {paper.totalScore} 分 | 及格: {paper.passScore} 分
                  </p>
                  <p>平均分: {paper.averageScore} 分</p>
                </div>

                <div className="flex items-center justify-between text-xs text-muted-foreground">
                  <span>创建者: {paper.createdBy}</span>
                  <span>{paper.createdDate}</span>
                </div>

                <div className="flex space-x-2">
                  <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                    <Eye className="mr-1 h-3 w-3" />
                    预览
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1 bg-transparent">
                    <Edit className="mr-1 h-3 w-3" />
                    编辑
                  </Button>
                  <Button size="sm" variant="outline">
                    <Copy className="h-3 w-3" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Download className="h-3 w-3" />
                  </Button>
                  <Button size="sm" variant="outline">
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredPapers.length === 0 && (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">暂无试卷</h3>
            <p className="text-muted-foreground text-center mb-4">
              没有找到符合条件的试卷，请尝试调整搜索条件或创建新试卷。
            </p>
            <Button onClick={() => setIsCreateOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              创建试卷
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
