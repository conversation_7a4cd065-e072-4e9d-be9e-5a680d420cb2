"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Download, Eye, Clock, BookOpen, PlayCircle, CheckCircle, Award } from "lucide-react"

// 模拟数据
const learningRecords = [
  {
    id: 1,
    studentName: "张三",
    studentId: "ST001",
    materialTitle: "船舶安全操作手册",
    materialType: "document",
    category: "安全培训",
    startTime: "2024-01-15 09:00",
    endTime: "2024-01-15 10:30",
    duration: 90,
    progress: 100,
    status: "completed",
    score: 85,
    attempts: 1,
  },
  {
    id: 2,
    studentName: "李四",
    studentId: "ST002",
    materialTitle: "海上求生技能培训视频",
    materialType: "video",
    category: "求生技能",
    startTime: "2024-01-14 14:00",
    endTime: "2024-01-14 15:45",
    duration: 105,
    progress: 75,
    status: "in_progress",
    score: null,
    attempts: 1,
  },
  {
    id: 3,
    studentName: "王五",
    studentId: "ST003",
    materialTitle: "船舶消防系统图解",
    materialType: "image",
    category: "消防安全",
    startTime: "2024-01-13 16:00",
    endTime: "2024-01-13 16:45",
    duration: 45,
    progress: 100,
    status: "completed",
    score: 92,
    attempts: 2,
  },
  {
    id: 4,
    studentName: "赵六",
    studentId: "ST004",
    materialTitle: "国际海事法规汇编",
    materialType: "document",
    category: "法规培训",
    startTime: "2024-01-12 10:00",
    endTime: null,
    duration: 30,
    progress: 40,
    status: "paused",
    score: null,
    attempts: 1,
  },
]

const students = ["全部学员", "张三", "李四", "王五", "赵六"]
const categories = ["全部分类", "安全培训", "求生技能", "消防安全", "法规培训"]
const statuses = ["全部状态", "已完成", "进行中", "已暂停", "未开始"]

export default function LearningRecordsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedStudent, setSelectedStudent] = useState("全部学员")
  const [selectedCategory, setSelectedCategory] = useState("全部分类")
  const [selectedStatus, setSelectedStatus] = useState("全部状态")

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800">已完成</Badge>
      case "in_progress":
        return <Badge className="bg-blue-100 text-blue-800">进行中</Badge>
      case "paused":
        return <Badge className="bg-yellow-100 text-yellow-800">已暂停</Badge>
      case "not_started":
        return <Badge className="bg-gray-100 text-gray-800">未开始</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "document":
        return <BookOpen className="h-4 w-4" />
      case "video":
        return <PlayCircle className="h-4 w-4" />
      default:
        return <BookOpen className="h-4 w-4" />
    }
  }

  const filteredRecords = learningRecords.filter((record) => {
    const matchesSearch =
      record.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.materialTitle.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStudent = selectedStudent === "全部学员" || record.studentName === selectedStudent
    const matchesCategory = selectedCategory === "全部分类" || record.category === selectedCategory
    const matchesStatus =
      selectedStatus === "全部状态" ||
      (selectedStatus === "已完成" && record.status === "completed") ||
      (selectedStatus === "进行中" && record.status === "in_progress") ||
      (selectedStatus === "已暂停" && record.status === "paused") ||
      (selectedStatus === "未开始" && record.status === "not_started")

    return matchesSearch && matchesStudent && matchesCategory && matchesStatus
  })

  // 统计数据
  const totalRecords = learningRecords.length
  const completedRecords = learningRecords.filter((r) => r.status === "completed").length
  const inProgressRecords = learningRecords.filter((r) => r.status === "in_progress").length
  const averageScore =
    learningRecords.filter((r) => r.score !== null).reduce((sum, r) => sum + r.score!, 0) /
    learningRecords.filter((r) => r.score !== null).length

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">学习记录</h2>
          <p className="text-muted-foreground">查看和管理学员的学习进度记录</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出记录
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="flex flex-col space-y-4 md:flex-row md:space-y-0 md:space-x-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索学员或学习资料..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>
        <Select value={selectedStudent} onValueChange={setSelectedStudent}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择学员" />
          </SelectTrigger>
          <SelectContent>
            {students.map((student) => (
              <SelectItem key={student} value={student}>
                {student}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择分类" />
          </SelectTrigger>
          <SelectContent>
            {categories.map((category) => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        <Select value={selectedStatus} onValueChange={setSelectedStatus}>
          <SelectTrigger className="w-[150px]">
            <SelectValue placeholder="选择状态" />
          </SelectTrigger>
          <SelectContent>
            {statuses.map((status) => (
              <SelectItem key={status} value={status}>
                {status}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总学习记录</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalRecords}</div>
            <p className="text-xs text-muted-foreground">+3 较昨日</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">已完成</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedRecords}</div>
            <p className="text-xs text-muted-foreground">
              完成率 {Math.round((completedRecords / totalRecords) * 100)}%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">进行中</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{inProgressRecords}</div>
            <p className="text-xs text-muted-foreground">活跃学习中</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均分数</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(averageScore)}</div>
            <p className="text-xs text-muted-foreground">+2 较上周</p>
          </CardContent>
        </Card>
      </div>

      {/* 学习记录列表 */}
      <Card>
        <CardHeader>
          <CardTitle>学习记录详情</CardTitle>
          <CardDescription>显示所有学员的学习进度和完成情况</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredRecords.map((record) => (
              <div
                key={record.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50"
              >
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    {getTypeIcon(record.materialType)}
                    <div>
                      <h4 className="font-medium">{record.materialTitle}</h4>
                      <p className="text-sm text-muted-foreground">{record.category}</p>
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-6">
                  <div className="text-center">
                    <p className="text-sm font-medium">{record.studentName}</p>
                    <p className="text-xs text-muted-foreground">{record.studentId}</p>
                  </div>

                  <div className="text-center min-w-[100px]">
                    <div className="flex items-center space-x-2 mb-1">
                      <Progress value={record.progress} className="w-16" />
                      <span className="text-xs">{record.progress}%</span>
                    </div>
                    {getStatusBadge(record.status)}
                  </div>

                  <div className="text-center">
                    <p className="text-sm font-medium">{record.score ? `${record.score}分` : "-"}</p>
                    <p className="text-xs text-muted-foreground">尝试 {record.attempts} 次</p>
                  </div>

                  <div className="text-center">
                    <p className="text-sm">{record.duration}分钟</p>
                    <p className="text-xs text-muted-foreground">{record.startTime.split(" ")[0]}</p>
                  </div>

                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <Eye className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredRecords.length === 0 && (
            <div className="flex flex-col items-center justify-center py-12">
              <BookOpen className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">暂无学习记录</h3>
              <p className="text-muted-foreground text-center">没有找到符合条件的学习记录，请尝试调整筛选条件。</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
