"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from "recharts"
import { TrendingUp, Users, BookOpen, Clock, Award, Target, Download } from "lucide-react"

// 模拟数据
const learningStats = {
  totalStudents: 156,
  activeStudents: 89,
  completedCourses: 234,
  totalStudyTime: 1456,
  averageScore: 87,
  completionRate: 76,
}

const monthlyProgress = [
  { month: "1月", completed: 45, inProgress: 23, total: 68 },
  { month: "2月", completed: 52, inProgress: 28, total: 80 },
  { month: "3月", completed: 38, inProgress: 31, total: 69 },
  { month: "4月", completed: 61, inProgress: 25, total: 86 },
  { month: "5月", completed: 49, inProgress: 33, total: 82 },
  { month: "6月", completed: 58, inProgress: 29, total: 87 },
]

const categoryStats = [
  { name: "安全培训", value: 35, color: "#0088FE" },
  { name: "求生技能", value: 25, color: "#00C49F" },
  { name: "消防安全", value: 20, color: "#FFBB28" },
  { name: "法规培训", value: 15, color: "#FF8042" },
  { name: "技能操作", value: 5, color: "#8884D8" },
]

const performanceData = [
  { subject: "安全知识", A: 85, B: 78, fullMark: 100 },
  { subject: "求生技能", A: 92, B: 85, fullMark: 100 },
  { subject: "消防安全", A: 78, B: 82, fullMark: 100 },
  { subject: "法规理解", A: 88, B: 75, fullMark: 100 },
  { subject: "实操能力", A: 82, B: 88, fullMark: 100 },
  { subject: "应急处理", A: 90, B: 80, fullMark: 100 },
]

const topPerformers = [
  { name: "张三", score: 95, courses: 8, time: 120 },
  { name: "李四", score: 92, courses: 7, time: 98 },
  { name: "王五", score: 90, courses: 9, time: 145 },
  { name: "赵六", score: 88, courses: 6, time: 87 },
  { name: "钱七", score: 87, courses: 8, time: 112 },
]

export default function LearningSummaryPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("本月")
  const [selectedCategory, setSelectedCategory] = useState("全部分类")

  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">学习总结</h2>
          <p className="text-muted-foreground">学习数据分析和总结报告</p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="本周">本周</SelectItem>
              <SelectItem value="本月">本月</SelectItem>
              <SelectItem value="本季度">本季度</SelectItem>
              <SelectItem value="本年">本年</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            导出报告
          </Button>
        </div>
      </div>

      {/* 关键指标 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总学员数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{learningStats.totalStudents}</div>
            <p className="text-xs text-muted-foreground">+12% 较上月</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">活跃学员</CardTitle>
            <Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{learningStats.activeStudents}</div>
            <p className="text-xs text-muted-foreground">
              活跃率 {Math.round((learningStats.activeStudents / learningStats.totalStudents) * 100)}%
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">完成课程</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{learningStats.completedCourses}</div>
            <p className="text-xs text-muted-foreground">+18% 较上月</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">学习时长</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{learningStats.totalStudyTime}h</div>
            <p className="text-xs text-muted-foreground">
              平均 {Math.round(learningStats.totalStudyTime / learningStats.activeStudents)}h/人
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">平均分数</CardTitle>
            <Award className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{learningStats.averageScore}</div>
            <p className="text-xs text-muted-foreground">+3% 较上月</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">完成率</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{learningStats.completionRate}%</div>
            <p className="text-xs text-muted-foreground">+5% 较上月</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="progress">进度分析</TabsTrigger>
          <TabsTrigger value="performance">成绩分析</TabsTrigger>
          <TabsTrigger value="ranking">排行榜</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>月度学习进度</CardTitle>
                <CardDescription>显示每月的学习完成情况和进度</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    completed: {
                      label: "已完成",
                      color: "hsl(var(--chart-1))",
                    },
                    inProgress: {
                      label: "进行中",
                      color: "hsl(var(--chart-2))",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={monthlyProgress}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis />
                      <ChartTooltip content={<ChartTooltipContent />} />
                      <Bar dataKey="completed" fill="var(--color-completed)" />
                      <Bar dataKey="inProgress" fill="var(--color-inProgress)" />
                    </BarChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>学习分类分布</CardTitle>
                <CardDescription>不同培训类别的学习分布情况</CardDescription>
              </CardHeader>
              <CardContent>
                <ChartContainer
                  config={{
                    value: {
                      label: "学习量",
                    },
                  }}
                  className="h-[300px]"
                >
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={categoryStats}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {categoryStats.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <ChartTooltip content={<ChartTooltipContent />} />
                    </PieChart>
                  </ResponsiveContainer>
                </ChartContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="progress" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>学习进度趋势</CardTitle>
              <CardDescription>显示学习完成数量的月度趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                config={{
                  total: {
                    label: "总学习量",
                    color: "hsl(var(--chart-1))",
                  },
                }}
                className="h-[400px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={monthlyProgress}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="month" />
                    <YAxis />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Line type="monotone" dataKey="total" stroke="var(--color-total)" strokeWidth={2} />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>能力雷达图</CardTitle>
              <CardDescription>显示不同技能领域的平均表现</CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer
                config={{
                  A: {
                    label: "优秀组",
                    color: "hsl(var(--chart-1))",
                  },
                  B: {
                    label: "普通组",
                    color: "hsl(var(--chart-2))",
                  },
                }}
                className="h-[400px]"
              >
                <ResponsiveContainer width="100%" height="100%">
                  <RadarChart data={performanceData}>
                    <PolarGrid />
                    <PolarAngleAxis dataKey="subject" />
                    <PolarRadiusAxis />
                    <Radar name="优秀组" dataKey="A" stroke="var(--color-A)" fill="var(--color-A)" fillOpacity={0.6} />
                    <Radar name="普通组" dataKey="B" stroke="var(--color-B)" fill="var(--color-B)" fillOpacity={0.6} />
                    <ChartTooltip content={<ChartTooltipContent />} />
                  </RadarChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ranking" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>学习排行榜</CardTitle>
              <CardDescription>根据综合表现排名的优秀学员</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topPerformers.map((performer, index) => (
                  <div key={performer.name} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div
                        className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-bold ${
                          index === 0
                            ? "bg-yellow-500"
                            : index === 1
                              ? "bg-gray-400"
                              : index === 2
                                ? "bg-amber-600"
                                : "bg-blue-500"
                        }`}
                      >
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{performer.name}</h4>
                        <p className="text-sm text-muted-foreground">完成 {performer.courses} 门课程</p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <p className="text-lg font-bold">{performer.score}</p>
                        <p className="text-xs text-muted-foreground">平均分</p>
                      </div>
                      <div className="text-center">
                        <p className="text-lg font-bold">{performer.time}h</p>
                        <p className="text-xs text-muted-foreground">学习时长</p>
                      </div>
                      <Badge variant={index < 3 ? "default" : "secondary"}>{index < 3 ? "优秀" : "良好"}</Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
