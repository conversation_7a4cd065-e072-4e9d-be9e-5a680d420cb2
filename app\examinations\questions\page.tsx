"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Search,
  Plus,
  MoreHorizontal,
  FileQuestion,
  Brain,
  Edit,
  Trash2,
  Copy,
  Eye,
  Target,
  Clock,
  BarChart3,
} from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"

const questionCategories = [
  "化学品船安全",
  "油轮操作",
  "STCW基本安全",
  "应急响应",
  "环境保护",
  "货物装卸",
  "导航安全",
  "机舱操作",
]

const difficultyLevels = ["简单", "中等", "困难", "专家"]

const questionTypes = ["单选题", "判断题", "填空题", "问答题"]

const sampleQuestions = [
  {
    id: 1,
    question: "根据IMDG规则，化学品在运输过程中必须保持的最低温度是多少？",
    type: "单选题",
    category: "化学品船安全",
    difficulty: "中等",
    options: ["高于闪点", "低于闪点", "室温", "冰点"],
    correctAnswer: "低于闪点",
    explanation: "化学品必须保持在闪点以下以防止运输过程中发生点燃。",
    createdBy: "AI生成器",
    createdDate: "2024-01-15",
    usageCount: 45,
    successRate: 78,
  },
  {
    id: 2,
    question: "检测到气体泄漏后，必须在多少秒内启动紧急关闭程序？",
    type: "单选题",
    category: "应急响应",
    difficulty: "困难",
    options: ["30秒", "60秒", "90秒", "120秒"],
    correctAnswer: "30秒",
    explanation: "IMO法规要求在30秒内立即响应以防止事态升级。",
    createdBy: "张三",
    createdDate: "2024-01-12",
    usageCount: 32,
    successRate: 65,
  },
  {
    id: 3,
    question: "MARPOL公约主要涉及海事操作的哪个方面？",
    type: "单选题",
    category: "环境保护",
    difficulty: "简单",
    options: ["船员安全程序", "海洋污染防治", "导航要求", "货物装载程序"],
    correctAnswer: "海洋污染防治",
    explanation: "MARPOL是《国际防止船舶造成污染公约》。",
    createdBy: "AI生成器",
    createdDate: "2024-01-18",
    usageCount: 67,
    successRate: 89,
  },
  {
    id: 4,
    question: "油轮货舱在装载前必须进行惰化处理。对还是错？",
    type: "判断题",
    category: "油轮操作",
    difficulty: "中等",
    correctAnswer: "对",
    explanation: "惰化可防止货舱内形成爆炸性气体。",
    createdBy: "李四",
    createdDate: "2024-01-10",
    usageCount: 54,
    successRate: 82,
  },
]

export default function QuestionBankPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [filterCategory, setFilterCategory] = useState("all")
  const [filterDifficulty, setFilterDifficulty] = useState("all")
  const [filterType, setFilterType] = useState("all")

  const filteredQuestions = sampleQuestions.filter((question) => {
    const matchesSearch =
      question.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      question.category.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = filterCategory === "all" || question.category === filterCategory
    const matchesDifficulty = filterDifficulty === "all" || question.difficulty === filterDifficulty
    const matchesType = filterType === "all" || question.type === filterType
    return matchesSearch && matchesCategory && matchesDifficulty && matchesType
  })

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case "简单":
        return "bg-green-100 text-green-700"
      case "中等":
        return "bg-yellow-100 text-yellow-700"
      case "困难":
        return "bg-orange-100 text-orange-700"
      case "专家":
        return "bg-red-100 text-red-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case "单选题":
        return "bg-blue-100 text-blue-700"
      case "判断题":
        return "bg-purple-100 text-purple-700"
      case "填空题":
        return "bg-indigo-100 text-indigo-700"
      case "问答题":
        return "bg-pink-100 text-pink-700"
      default:
        return "bg-gray-100 text-gray-700"
    }
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">题库管理</h1>
            <p className="text-gray-600">创建、组织和管理考试题目</p>
          </div>
          <div className="flex items-center gap-4">
            <Button variant="outline">
              <Brain className="w-4 h-4 mr-2" />
              AI生成
            </Button>
            <Button variant="outline">
              <FileQuestion className="w-4 h-4 mr-2" />
              导入题目
            </Button>
            <Dialog>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="w-4 h-4 mr-2" />
                  添加题目
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>创建新题目</DialogTitle>
                  <DialogDescription>向题库添加新题目</DialogDescription>
                </DialogHeader>
                <div className="grid gap-6 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="category">类别</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择类别" />
                        </SelectTrigger>
                        <SelectContent>
                          {questionCategories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="difficulty">难度等级</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="选择难度" />
                        </SelectTrigger>
                        <SelectContent>
                          {difficultyLevels.map((level) => (
                            <SelectItem key={level} value={level}>
                              {level}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="type">题目类型</Label>
                    <RadioGroup defaultValue="single-choice">
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="single-choice" id="single-choice" />
                        <Label htmlFor="single-choice">单选题</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="true-false" id="true-false" />
                        <Label htmlFor="true-false">判断题</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="fill-blank" id="fill-blank" />
                        <Label htmlFor="fill-blank">填空题</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="essay" id="essay" />
                        <Label htmlFor="essay">问答题</Label>
                      </div>
                    </RadioGroup>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="question">题目内容</Label>
                    <Textarea id="question" placeholder="在此输入题目内容..." className="min-h-[100px]" />
                  </div>
                  <div className="space-y-4">
                    <Label>答案选项（单选题）</Label>
                    <div className="space-y-2">
                      <div className="flex items-center space-x-2">
                        <Checkbox id="option-a" />
                        <Label htmlFor="option-a" className="text-sm">
                          正确答案
                        </Label>
                        <Input placeholder="选项A" className="flex-1" />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="option-b" />
                        <Label htmlFor="option-b" className="text-sm">
                          正确答案
                        </Label>
                        <Input placeholder="选项B" className="flex-1" />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="option-c" />
                        <Label htmlFor="option-c" className="text-sm">
                          正确答案
                        </Label>
                        <Input placeholder="选项C" className="flex-1" />
                      </div>
                      <div className="flex items-center space-x-2">
                        <Checkbox id="option-d" />
                        <Label htmlFor="option-d" className="text-sm">
                          正确答案
                        </Label>
                        <Input placeholder="选项D" className="flex-1" />
                      </div>
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="explanation">解释说明</Label>
                    <Textarea id="explanation" placeholder="为正确答案提供解释说明..." className="min-h-[80px]" />
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button variant="outline">取消</Button>
                  <Button>创建题目</Button>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>
      </header>

      <div className="flex-1 p-6 space-y-6">
        {/* Statistics Cards */}
        <div className="grid gap-6 md:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">题目总数</CardTitle>
              <FileQuestion className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,247</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+23</span> 本周新增
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">AI生成</CardTitle>
              <Brain className="h-4 w-4 text-purple-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">456</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">36%</span> 占总题目
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">平均正确率</CardTitle>
              <Target className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">78.5%</div>
              <p className="text-xs text-muted-foreground">
                <span className="text-green-600">+2.3%</span> 较上月
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">最常用类别</CardTitle>
              <BarChart3 className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">化学品</div>
              <p className="text-xs text-muted-foreground">安全题目</p>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="按内容或类别搜索题目..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Select value={filterCategory} onValueChange={setFilterCategory}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="全部类别" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类别</SelectItem>
                  {questionCategories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={filterDifficulty} onValueChange={setFilterDifficulty}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="难度" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部难度</SelectItem>
                  {difficultyLevels.map((level) => (
                    <SelectItem key={level} value={level}>
                      {level}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="题目类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  {questionTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Questions List */}
        <Card>
          <CardHeader>
            <CardTitle>题库</CardTitle>
            <CardDescription>管理和组织所有考试题目</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredQuestions.map((question) => (
                <div key={question.id} className="border rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Badge className={getDifficultyColor(question.difficulty)}>{question.difficulty}</Badge>
                        <Badge className={getTypeColor(question.type)}>{question.type}</Badge>
                        <Badge variant="outline">{question.category}</Badge>
                        {question.createdBy === "AI生成器" && (
                          <Badge className="bg-purple-100 text-purple-700">
                            <Brain className="w-3 h-3 mr-1" />
                            AI生成
                          </Badge>
                        )}
                      </div>
                      <p className="font-medium text-gray-900 mb-2">{question.question}</p>
                      {question.type === "单选题" && question.options && (
                        <div className="grid grid-cols-2 gap-2 mb-3">
                          {question.options.map((option, index) => (
                            <div
                              key={index}
                              className={`text-sm p-2 rounded ${
                                option === question.correctAnswer
                                  ? "bg-green-50 text-green-700 border border-green-200"
                                  : "bg-gray-50 text-gray-600"
                              }`}
                            >
                              {String.fromCharCode(65 + index)}. {option}
                            </div>
                          ))}
                        </div>
                      )}
                      {question.type === "判断题" && (
                        <div className="mb-3">
                          <span className="text-sm bg-green-50 text-green-700 px-2 py-1 rounded border border-green-200">
                            正确答案: {question.correctAnswer}
                          </span>
                        </div>
                      )}
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          创建: {question.createdDate}
                        </span>
                        <span className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          使用: {question.usageCount} 次
                        </span>
                        <span className="flex items-center gap-1">
                          <Target className="h-3 w-3" />
                          正确率: {question.successRate}%
                        </span>
                        <span>创建者: {question.createdBy}</span>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>
                          <Eye className="w-4 h-4 mr-2" />
                          预览
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="w-4 h-4 mr-2" />
                          编辑
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Copy className="w-4 h-4 mr-2" />
                          复制
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-red-600">
                          <Trash2 className="w-4 h-4 mr-2" />
                          删除
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                  {question.explanation && (
                    <div className="mt-3 p-3 bg-blue-50 rounded border border-blue-200">
                      <p className="text-sm text-blue-800">
                        <strong>解释:</strong> {question.explanation}
                      </p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
